#wp-admin-bar-stamps-com .ab-icon:before {
	content: '\f463';
	top: 2px;
}
#wc_stamps_get_label ul.steps {
	overflow: hidden;
	margin: 0 -12px;
	padding: 2px;
	font-size: 0.85em;
}
#wc_stamps_get_label ul.steps li {
	border-bottom: 3px solid #d4f0c0;
	color: #ddd;
	margin: 0;
	float: left;
	padding: .5em 12px;
	text-align: center;
	width: 33.3%;
	white-space: nowrap;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
#wc_stamps_get_label ul.steps.needs-customs li {
	width: 25%;
}
#wc_stamps_get_label ul.steps li.active {
	color: #777;
	border-color: #7ad03a;
}
#wc_stamps_get_label .inside {
	margin: 0;
	padding: 0 12px;
}
#wc_stamps_get_label .widefat {
	width: 100%;
}
#wc_stamps_get_label .widefat .addons ul {
	margin: 0;
	padding: 0;
}
#wc_stamps_get_label .widefat .addons ul ul {
	margin: 0;
	padding: .5em 0 0 2em;
}
#wc_stamps_get_label .form-table {
	width: 100%;
}
#wc_stamps_get_label .form-table td, #wc_stamps_get_label .form-table th {
	padding: 6px 0;
	font-size: 0.92em;
}
#wc_stamps_get_label .form-table th {
	width: 35%;
	padding-right: 12px;
}
#wc_stamps_get_label .form-table td input, #wc_stamps_get_label .form-table td select {
	width: 100%;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
#wc_stamps_get_label .form-table td span.description {
	color: #999;
	display: block;
	margin: 3px 0 0 0;
}
#wc_stamps_get_label .inside h4 {
	padding: 8px 0;
	font-size: 14px;
	margin: 8px 0 0 0;
	border-bottom: 1px solid #eee;
}
#wc_stamps_get_label .label-preview {
	text-align: center;
	margin: 9px 0;
	border: 1px solid #eee;
	height: 64px;
	width: 64px;
	overflow: hidden;
	display: block;
	line-height: 64px;
}
#wc_stamps_get_label .label-preview img {
	max-width: 100%;
	height: auto;
	margin: 0;
}
#wc_stamps_get_label table.labels {
	margin: 9px 0 0;
}
#wc_stamps_get_label table.labels td {
	vertical-align: middle;
}
#wc_stamps_get_label table.labels td img {
	margin: 0;
	display: block;
}
#wc_stamps_get_label table.labels tr .label-actions {
	visibility: hidden;
	margin: 6px 0 0 0;
}
#wc_stamps_get_label table.labels tr:hover .label-actions {
	visibility: visible;
}
#wc_stamps_get_label table.labels .label-actions .cancel-label,
#wc_stamps_get_label table.labels .label-actions .delete-label {
	color: #a00;
}
#wc_stamps_get_label .wc-stamps-customs-item {
	background: #f5f5f5;
	padding: 9px;
	margin: 0 0 3px;
}
#wc_stamps_get_label .wc-stamps-customs-item table {
	margin: 0;
}
#wc_stamps_get_label .wc-stamps-customs-item .wc-stamps-customs-remove-line {
	text-align: right;
	display: block;
}

#wc_stamps_get_label .wc-stamps-surcharges {
	display: inline-block;
	position: relative;
}

#wc_stamps_get_label .wc-stamps-surcharges .woocommerce-help-tip {
	top: 2px;
	right: -18px;
	margin-left: 0px;
	position: absolute;
}

#wp-admin-bar-stamps-com.error-api {
	background-color: #a50d0d;
}

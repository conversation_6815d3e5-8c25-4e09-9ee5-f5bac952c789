/* global jQuery, window, wc_stamps_admin, Clipboard */
( function( $ ) {

	var self = {
		clipboardTriggers: ['.copy-tracking-number'],

		init: function() {
			self.initClipboard();
			self.initNonDeliveryNotice();
		},

		initClipboard: function() {
			$.each( self.clipboardTriggers, $.proxy( self.initClipboardHandler, self ) );
		},

		initClipboardHandler: function( _, selector ) {
			var clipboard = new Clipboard( selector );

			clipboard.on( 'success', $.proxy( self.onCopySuccess, self ) );
			clipboard.on( 'error', $.proxy( self.onCopyError, self ) );

			$( selector ).on( 'click', $.proxy( self.clipboard<PERSON>lickHandler, self ) );
		},

		clipboardClickHandler: function( e ) {
			e.preventDefault();
		},

		onCopySuccess: function( e ) {
			$( e.trigger ).tipTip( {
				'attribute': 'data-clipboard-success',
				'activation': 'focus',
				'fadeIn': 50,
				'fadeOut': 50,
				'delay': 0
			} ).focus();
		},

		onCopyError: function( e ) {
			self.clipboardFallback( e.text );
		},

		clipboardFallback: function( textToCopy ) {
			window.prompt( wc_stamps_admin.copy_to_clipboard_fallback_i18n, textToCopy );
		},

		initNonDeliveryNotice: function() {
			// Listen for Stamps widget initialization events
			$( document ).on( 'init', '#wc_stamps_get_label', function() {
				self.toggleReturnFeeNotice();
			});

			// Also check on page load
			self.toggleReturnFeeNotice();

			// Listen for changes
			$( document ).on( 'change', '#stamps_non_delivery_option', self.toggleReturnFeeNotice );
		},

		toggleReturnFeeNotice: function() {
			var $select = $( '#stamps_non_delivery_option' );
			var $notice = $( '#stamps_return_fee_notice' );

			// Only proceed if both elements exist
			if ( $select.length && $notice.length ) {
				var selectedValue = $select.val();

				if ( selectedValue === 'Return' ) {
					$notice.show();
				} else {
					$notice.hide();
				}
			}
		}
	}

	$( self.init );

} )( jQuery );

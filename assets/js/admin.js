/* global jQuery, window, wc_stamps_admin, Clipboard */
( function( $ ) {

	var self = {
		clipboardTriggers: ['.copy-tracking-number'],

		init: function() {
			self.initClipboard();
			self.initNonDeliveryNotice();
		},

		initClipboard: function() {
			$.each( self.clipboardTriggers, $.proxy( self.initClipboardHandler, self ) );
		},

		initClipboardHandler: function( _, selector ) {
			var clipboard = new Clipboard( selector );

			clipboard.on( 'success', $.proxy( self.onCopySuccess, self ) );
			clipboard.on( 'error', $.proxy( self.onCopyError, self ) );

			$( selector ).on( 'click', $.proxy( self.clipboard<PERSON>lickHandler, self ) );
		},

		clipboardClickHandler: function( e ) {
			e.preventDefault();
		},

		onCopySuccess: function( e ) {
			$( e.trigger ).tipTip( {
				'attribute': 'data-clipboard-success',
				'activation': 'focus',
				'fadeIn': 50,
				'fadeOut': 50,
				'delay': 0
			} ).focus();
		},

		onCopyError: function( e ) {
			self.clipboardFallback( e.text );
		},

		clipboardFallback: function( textToCopy ) {
			window.prompt( wc_stamps_admin.copy_to_clipboard_fallback_i18n, textToCopy );
		},

		initNonDeliveryNotice: function() {
			self.toggleReturnFeeNotice();
			$( document ).on( 'change', '#stamps_non_delivery_option', self.toggleReturnFeeNotice );
		},

		toggleReturnFeeNotice: function() {
			var selectedValue = $( '#stamps_non_delivery_option' ).val();
			var notice = $( '#stamps_return_fee_notice' );

			if ( selectedValue === 'Return' ) {
				notice.show();
			} else {
				notice.hide();
			}
		}
	}

	$( self.init );

} )( jQuery );

{"name": "woocommerce/woocommerce-shipping-stamps", "description": "Stamps.com API integration for label printing. Requires server SOAP support.", "homepage": "https://woocommerce.com/products/woocommerce-shipping-stamps/", "type": "wordpress-plugin", "license": "GPL-2.0+", "archive": {"exclude": ["!/assets", "!/languages", "bin", "tests", "/vendor", "Gruntfile.js", "README.md", "package.json", "package-lock.json", "composer.json", "composer.lock", "phpunit.xml.dist", "node_modules", "woocommerce-shipping-stamps.zip", "*.scss", ".*", "pnpm-lock.yaml"]}, "require-dev": {"woocommerce/qit-cli": "*", "squizlabs/php_codesniffer": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "wp-coding-standards/wpcs": "*", "woocommerce/woocommerce-sniffs": "*"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipping-stamps --zip=woocommerce-shipping-stamps.zip"]}}
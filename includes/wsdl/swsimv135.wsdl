<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135" xmlns:s2="http://stamps.com/xml/namespace/2014/3/addressservicev2" xmlns:s1="http://microsoft.com/wsdl/types/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" targetNamespace="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Stamps.com Web Services for Individual Meters (SWS/IM) Version 135</wsdl:documentation>
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135">
      <s:import namespace="http://microsoft.com/wsdl/types/"/>
      <s:import namespace="http://stamps.com/xml/namespace/2014/3/addressservicev2"/>
      <s:element name="GetRates">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
            <s:element minOccurs="0" maxOccurs="1" default="All" name="Carrier" type="tns:Carrier"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Credentials">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="IntegrationID" type="s1:guid"/>
          <s:element minOccurs="1" maxOccurs="1" name="Username" type="tns:string-0-40"/>
          <s:element minOccurs="1" maxOccurs="1" name="Password" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="RateV46">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="From" type="tns:Address"/>
          <s:element minOccurs="1" maxOccurs="1" name="To" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="Amount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="MaxAmount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="ServiceType" type="tns:ServiceType"/>
          <s:element minOccurs="0" maxOccurs="1" name="ServiceDescription" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PrintLayout" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="DeliverDays" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightLb" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightOz" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="PackageType" type="tns:PackageTypeV11"/>
          <s:element minOccurs="0" maxOccurs="1" name="RequiresAllOf" type="tns:ArrayOfArrayOfAddOnTypeV20"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="Length" type="tns:double-le-999"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="Width" type="tns:double-le-999"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="Height" type="tns:double-le-999"/>
          <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="ShipDate" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="DeliveryDate" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="InsuredValue" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="RegisteredValue" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="CODValue" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="DeclaredValue" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="NonMachinable" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" default="true" name="RectangularShaped" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="Prohibitions" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Restrictions" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Observations" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Regulations" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="GEMNotes" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="MaxDimensions" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="DimWeighting" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="AddOns" type="tns:ArrayOfAddOnV20"/>
          <s:element minOccurs="0" maxOccurs="1" name="Surcharges" type="tns:ArrayOfSurchargeV5"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="EffectiveWeightInOunces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="Zone" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="RateCategory" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="CubicPricing" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ContentType" type="tns:ContentTypeV2"/>
          <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="EntryFacility" type="tns:EntryFacilityV1"/>
          <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="SortType" type="tns:SortTypeV1"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Address">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="FullName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="NamePrefix" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="FirstName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="MiddleName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="LastName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="NameSuffix" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Title" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Department" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Company" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address1" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address2" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address3" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="City" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="State" type="tns:string-0-30"/>
          <s:element minOccurs="0" maxOccurs="1" name="ZIPCode" type="tns:string-0-5"/>
          <s:element minOccurs="0" maxOccurs="1" name="ZIPCodeAddOn" type="tns:string-0-4"/>
          <s:element minOccurs="0" maxOccurs="1" name="DPB" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CheckDigit" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Province" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PostalCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="US" name="Country" type="tns:string-2-2"/>
          <s:element minOccurs="0" maxOccurs="1" name="Urbanization" type="tns:string-0-28"/>
          <s:element minOccurs="0" maxOccurs="1" name="PhoneNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CleanseHash" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="OverrideHash" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="EmailAddress" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="FullAddress" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ServiceType">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="US-FC"/>
          <s:enumeration value="US-PM"/>
          <s:enumeration value="US-XM"/>
          <s:enumeration value="US-MM"/>
          <s:enumeration value="US-BP"/>
          <s:enumeration value="US-LM"/>
          <s:enumeration value="US-EMI"/>
          <s:enumeration value="US-PMI"/>
          <s:enumeration value="US-FCI"/>
          <s:enumeration value="US-CM"/>
          <s:enumeration value="US-PS"/>
          <s:enumeration value="DHL-PE"/>
          <s:enumeration value="DHL-PG"/>
          <s:enumeration value="DHL-BPME"/>
          <s:enumeration value="DHL-BPMG"/>
          <s:enumeration value="DHL-MPE"/>
          <s:enumeration value="DHL-MPG"/>
          <s:enumeration value="AS-IPA"/>
          <s:enumeration value="AS-ISAL"/>
          <s:enumeration value="AS-EPKT"/>
          <s:enumeration value="DHL-PIPA"/>
          <s:enumeration value="GG-IPA"/>
          <s:enumeration value="GG-ISAL"/>
          <s:enumeration value="GG-EPKT"/>
          <s:enumeration value="IBC-IPA"/>
          <s:enumeration value="IBC-ISAL"/>
          <s:enumeration value="IBC-EPKT"/>
          <s:enumeration value="RRD-IPA"/>
          <s:enumeration value="RRD-ISAL"/>
          <s:enumeration value="RRD-EPKT"/>
          <s:enumeration value="AS-GNRC"/>
          <s:enumeration value="GG-GNRC"/>
          <s:enumeration value="RRD-GNRC"/>
          <s:enumeration value="SC-GPE"/>
          <s:enumeration value="SC-GPP"/>
          <s:enumeration value="SC-GPESS"/>
          <s:enumeration value="SC-GPPSS"/>
          <s:enumeration value="DHL-EWW"/>
          <s:enumeration value="FX-GD"/>
          <s:enumeration value="FX-HD"/>
          <s:enumeration value="FX-2D"/>
          <s:enumeration value="FX-ES"/>
          <s:enumeration value="FX-SO"/>
          <s:enumeration value="FX-PO"/>
          <s:enumeration value="FX-GDI"/>
          <s:enumeration value="FX-EI"/>
          <s:enumeration value="FX-PI"/>
          <s:enumeration value="SC-GPLSS"/>
          <s:enumeration value="US-RG"/>
          <s:enumeration value="SC-GPL"/>
          <s:enumeration value="UPS-NDAE"/>
          <s:enumeration value="UPS-NDA"/>
          <s:enumeration value="UPS-NDAS"/>
          <s:enumeration value="UPS-2DAA"/>
          <s:enumeration value="UPS-2DA"/>
          <s:enumeration value="UPS-3DS"/>
          <s:enumeration value="UPS-GD"/>
          <s:enumeration value="UPS-SD"/>
          <s:enumeration value="UPS-WES"/>
          <s:enumeration value="UPS-WESP"/>
          <s:enumeration value="UPS-WED"/>
          <s:enumeration value="UPS-WSR"/>
          <s:enumeration value="SC-GPFCI"/>
          <s:enumeration value="SC-GPFCISS"/>
          <s:enumeration value="SC-GPPMI"/>
          <s:enumeration value="SC-GPPMISS"/>
          <s:enumeration value="SC-GPEMI"/>
          <s:enumeration value="SC-GPEMISS"/>
          <s:enumeration value="SC-GPFCSS"/>
          <s:enumeration value="SC-GPPSSS"/>
          <s:enumeration value="CP-PM"/>
          <s:enumeration value="CP-XP"/>
          <s:enumeration value="CP-EP"/>
          <s:enumeration value="CP-RP"/>
          <s:enumeration value="CP-PMW"/>
          <s:enumeration value="CP-PMWU"/>
          <s:enumeration value="CP-XPI"/>
          <s:enumeration value="CP-XPU"/>
          <s:enumeration value="CP-EPU"/>
          <s:enumeration value="CP-TPI"/>
          <s:enumeration value="CP-TPU"/>
          <s:enumeration value="CP-SPIA"/>
          <s:enumeration value="CP-SPIS"/>
          <s:enumeration value="CP-SPUA"/>
          <s:enumeration value="CP-IPA"/>
          <s:enumeration value="CP-IPS"/>
          <s:enumeration value="FX-2DAM"/>
          <s:enumeration value="FX-FO"/>
          <s:enumeration value="FX-FI"/>
          <s:enumeration value="US-RETURN"/>
          <s:enumeration value="DHL-PIPL"/>
          <s:enumeration value="DHL-PIS"/>
          <s:enumeration value="DHL-PI"/>
          <s:enumeration value="DHL-PID"/>
          <s:enumeration value="DHL-PEM"/>
          <s:enumeration value="UPS-GDS"/>
          <s:enumeration value="US-GA"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PackageTypeV11">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="Postcard"/>
          <s:enumeration value="Letter"/>
          <s:enumeration value="Large Envelope or Flat"/>
          <s:enumeration value="Thick Envelope"/>
          <s:enumeration value="Package"/>
          <s:enumeration value="Flat Rate Box"/>
          <s:enumeration value="Small Flat Rate Box"/>
          <s:enumeration value="Large Flat Rate Box"/>
          <s:enumeration value="Flat Rate Envelope"/>
          <s:enumeration value="Flat Rate Padded Envelope"/>
          <s:enumeration value="Large Package"/>
          <s:enumeration value="Oversized Package"/>
          <s:enumeration value="Regional Rate Box A"/>
          <s:enumeration value="Regional Rate Box B"/>
          <s:enumeration value="Legal Flat Rate Envelope"/>
          <s:enumeration value="Regional Rate Box C"/>
          <s:enumeration value="OpenAndDistribute Half Tray Box"/>
          <s:enumeration value="OpenAndDistribute Full Tray Box"/>
          <s:enumeration value="OpenAndDistribute EMM Tray Box"/>
          <s:enumeration value="OpenAndDistribute Flat Tub Tray Box"/>
          <s:enumeration value="Express Envelope"/>
          <s:enumeration value="Documents"/>
          <s:enumeration value="Envelope"/>
          <s:enumeration value="Pak"/>
          <s:enumeration value="10 KG Box"/>
          <s:enumeration value="25 KG Box"/>
          <s:enumeration value="Express Box Large"/>
          <s:enumeration value="Express Box Medium"/>
          <s:enumeration value="Express Box Small"/>
          <s:enumeration value="Tube"/>
          <s:enumeration value="One Rate Envelope"/>
          <s:enumeration value="One Rate Small Box"/>
          <s:enumeration value="One Rate Medium Box"/>
          <s:enumeration value="One Rate Large Box"/>
          <s:enumeration value="One Rate Extra Large Box"/>
          <s:enumeration value="One Rate Pak"/>
          <s:enumeration value="One Rate Tube"/>
          <s:enumeration value="Box"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfArrayOfAddOnTypeV20">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="RequiresOneOf" nillable="true" type="tns:ArrayOfAddOnTypeV20"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfAddOnTypeV20">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="AddOnTypeV20" type="tns:AddOnTypeV20"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AddOnTypeV20">
        <s:restriction base="s:string">
          <s:enumeration value="US-A-INS"/>
          <s:enumeration value="US-A-COD"/>
          <s:enumeration value="US-A-DC"/>
          <s:enumeration value="US-A-SC"/>
          <s:enumeration value="US-A-COM"/>
          <s:enumeration value="US-A-CM"/>
          <s:enumeration value="US-A-RR"/>
          <s:enumeration value="US-A-RRM"/>
          <s:enumeration value="US-A-REG"/>
          <s:enumeration value="US-A-RD"/>
          <s:enumeration value="US-A-SH"/>
          <s:enumeration value="SC-A-INS"/>
          <s:enumeration value="SC-A-INSRM"/>
          <s:enumeration value="SC-A-HP"/>
          <s:enumeration value="US-A-SR"/>
          <s:enumeration value="US-A-NDW"/>
          <s:enumeration value="US-A-ESH"/>
          <s:enumeration value="US-A-NND"/>
          <s:enumeration value="US-A-RRE"/>
          <s:enumeration value="US-A-LANS"/>
          <s:enumeration value="US-A-LAWS"/>
          <s:enumeration value="US-A-HM"/>
          <s:enumeration value="US-A-CR"/>
          <s:enumeration value="US-A-1030"/>
          <s:enumeration value="US-A-ASR"/>
          <s:enumeration value="US-A-ASRD"/>
          <s:enumeration value="US-A-PR"/>
          <s:enumeration value="US-A-HFPU"/>
          <s:enumeration value="SC-A-POU"/>
          <s:enumeration value="CAR-A-SAT"/>
          <s:enumeration value="CAR-A-RES"/>
          <s:enumeration value="CAR-A-NSP"/>
          <s:enumeration value="CAR-A-ISR"/>
          <s:enumeration value="CAR-A-DSR"/>
          <s:enumeration value="CAR-A-ASR"/>
          <s:enumeration value="US-A-DDP"/>
          <s:enumeration value="US-A-POUR"/>
          <s:enumeration value="PG-A-INS"/>
          <s:enumeration value="CAR-A-DC"/>
          <s:enumeration value="CAR-A-SR"/>
          <s:enumeration value="CAR-A-INS"/>
          <s:enumeration value="CAR-A-IL"/>
          <s:enumeration value="CAR-A-POUR"/>
          <s:enumeration value="CAR-A-DDP"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfAddOnV20">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="AddOnV20" nillable="true" type="tns:AddOnV20"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AddOnV20">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="Amount" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="AddOnType" type="tns:AddOnTypeV20"/>
          <s:element minOccurs="0" maxOccurs="1" name="AddOnDescription" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="RequiresAllOf" type="tns:ArrayOfArrayOfAddOnTypeV20"/>
          <s:element minOccurs="0" maxOccurs="1" name="ProhibitedWithAnyOf" type="tns:ArrayOfAddOnTypeV20"/>
          <s:element minOccurs="0" maxOccurs="1" name="MissingData" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSurchargeV5">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="SurchargeV5" nillable="true" type="tns:SurchargeV5"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SurchargeV5">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="Amount" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="SurchargeType" type="tns:SurchargeTypeV5"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="SurchargeTypeV5">
        <s:restriction base="s:string">
          <s:enumeration value="SUR-A-ER"/>
          <s:enumeration value="SUR-A-EV"/>
          <s:enumeration value="SUR-A-FUEL"/>
          <s:enumeration value="SUR-A-OS"/>
          <s:enumeration value="SUR-A-OW"/>
          <s:enumeration value="SUR-A-RAD"/>
          <s:enumeration value="SUR-A-RD"/>
          <s:enumeration value="SUR-A-AHS"/>
          <s:enumeration value="SUR-A-NCS"/>
          <s:enumeration value="SUR-A-DAS"/>
          <s:enumeration value="SUR-A-DAE"/>
          <s:enumeration value="SUR-A-RAE"/>
          <s:enumeration value="SUR-A-NM"/>
          <s:enumeration value="SUR-A-NSDIM1"/>
          <s:enumeration value="SUR-A-NSDIM2"/>
          <s:enumeration value="SUR-A-NSVOL"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ContentTypeV2">
        <s:restriction base="s:string">
          <s:enumeration value="Commercial Sample"/>
          <s:enumeration value="Gift"/>
          <s:enumeration value="Document"/>
          <s:enumeration value="Returned Goods"/>
          <s:enumeration value="Other"/>
          <s:enumeration value="Merchandise"/>
          <s:enumeration value="Humanitarian Donation"/>
          <s:enumeration value="Dangerous Goods"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="EntryFacilityV1">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="None"/>
          <s:enumeration value="DDU"/>
          <s:enumeration value="ADC"/>
          <s:enumeration value="ASF"/>
          <s:enumeration value="NDC"/>
          <s:enumeration value="SCF"/>
          <s:enumeration value="Other"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="SortTypeV1">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="FiveDigit"/>
          <s:enumeration value="ThreeDigit"/>
          <s:enumeration value="NDC"/>
          <s:enumeration value="SCF"/>
          <s:enumeration value="MixedNDC"/>
          <s:enumeration value="Other"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="Carrier">
        <s:restriction base="s:string">
          <s:enumeration value="All"/>
          <s:enumeration value="USPS"/>
          <s:enumeration value="FedEx"/>
          <s:enumeration value="DHLExpress"/>
          <s:enumeration value="UPS"/>
          <s:enumeration value="CanadaPost"/>
          <s:enumeration value="DhlECommerce"/>
          <s:enumeration value="Zorbit"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetRatesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Rates" type="tns:ArrayOfRateV46"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfRateV46">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Rate" nillable="true" type="tns:RateV46"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateIndicium">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxID" type="tns:string-1-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="TrackingNumber" type="tns:string-0-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnTo" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" name="CustomerID" type="tns:string-1-64"/>
            <s:element minOccurs="0" maxOccurs="1" name="Customs" type="tns:CustomsV8"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SampleOnly" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="Normal" name="PostageMode" type="tns:PostageMode"/>
            <s:element minOccurs="0" maxOccurs="1" default="Auto" name="ImageType" type="tns:ImageType"/>
            <s:element minOccurs="0" maxOccurs="1" default="Default" name="EltronPrinterDPIType" type="tns:EltronPrinterDPIType"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="memo" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="cost_code_id" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="deliveryNotification" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ShipmentNotification" type="tns:ShipmentNotification"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="rotationDegrees" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="horizontalOffset" nillable="true" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="verticalOffset" nillable="true" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="printDensity" nillable="true" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="printMemo" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="printInstructions" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="requestPostageHash" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="Undefined" name="nonDeliveryOption" type="tns:NonDeliveryOption"/>
            <s:element minOccurs="0" maxOccurs="1" name="RedirectTo" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="OutboundTransactionID" type="tns:string-0-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="OriginalPostageHash" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnImageData" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="InternalTransactionNumber" type="tns:string-1-35"/>
            <s:element minOccurs="0" maxOccurs="1" default="Default" name="PaperSize" type="tns:PaperSizeV1"/>
            <s:element minOccurs="0" maxOccurs="1" name="EmailLabelTo" type="tns:LabelRecipientInfo"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="PayOnPrint" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnLabelExpirationDays" nillable="true" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="ImageDpiDefault" name="ImageDpi" type="tns:ImageDpi"/>
            <s:element minOccurs="0" maxOccurs="1" name="RateToken" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="OrderId" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="BypassCleanseAddress" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="ImageId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnIndiciumData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ExtendedPostageInfo" type="tns:ExtendedPostageInfoV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="EnclosedServiceType" type="tns:EnclosedServiceType"/>
            <s:element minOccurs="0" maxOccurs="1" default="Unknown" name="EnclosedPackageType" type="tns:EnclosedPackageType"/>
            <s:element minOccurs="0" maxOccurs="1" name="OrderDetails" type="tns:OrderDetails"/>
            <s:element minOccurs="0" maxOccurs="1" name="BrandingId" nillable="true" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="NotificationSettingId" nillable="true" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="GroupCode" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="CustomsV8">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ContentType" type="tns:ContentTypeV2"/>
          <s:element minOccurs="0" maxOccurs="1" name="Comments" type="tns:string-0-76"/>
          <s:element minOccurs="0" maxOccurs="1" name="LicenseNumber" type="tns:string-0-16"/>
          <s:element minOccurs="0" maxOccurs="1" name="CertificateNumber" type="tns:string-0-12"/>
          <s:element minOccurs="0" maxOccurs="1" name="InvoiceNumber" type="tns:string-0-15"/>
          <s:element minOccurs="0" maxOccurs="1" name="OtherDescribe" type="tns:string-0-25"/>
          <s:element minOccurs="1" maxOccurs="1" name="CustomsLines" type="tns:ArrayOfCustomsLine"/>
          <s:element minOccurs="0" maxOccurs="1" name="CustomsSigner" type="tns:string-2-100"/>
          <s:element minOccurs="0" maxOccurs="1" name="PassportNumber" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="PassportIssueDate" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="PassportExpiryDate" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="ImportLicenseNumber" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="SendersCustomsReference" type="tns:string-0-14"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfCustomsLine">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CustomsLine" nillable="true" type="tns:CustomsLine"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CustomsLine">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Description" type="tns:string-2-60"/>
          <s:element minOccurs="1" maxOccurs="1" name="Quantity" type="tns:double-ge-1"/>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="tns:decimal-ge-0"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightLb" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightOz" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" name="HSTariffNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CountryOfOrigin" type="tns:string-2-2"/>
          <s:element minOccurs="0" maxOccurs="1" name="sku" type="tns:string-0-20"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PostageMode">
        <s:restriction base="s:string">
          <s:enumeration value="Normal"/>
          <s:enumeration value="NoPostage"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ImageType">
        <s:restriction base="s:string">
          <s:enumeration value="Auto"/>
          <s:enumeration value="Png"/>
          <s:enumeration value="Gif"/>
          <s:enumeration value="Pdf"/>
          <s:enumeration value="Epl"/>
          <s:enumeration value="Jpg"/>
          <s:enumeration value="PrintOncePdf"/>
          <s:enumeration value="EncryptedPngUrl"/>
          <s:enumeration value="Zpl"/>
          <s:enumeration value="AZpl"/>
          <s:enumeration value="BZpl"/>
          <s:enumeration value="Bmp"/>
          <s:enumeration value="BmpMonochrome"/>
          <s:enumeration value="PngMonochrome"/>
          <s:enumeration value="JpgMonochrome"/>
          <s:enumeration value="GifMonochrome"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="EltronPrinterDPIType">
        <s:restriction base="s:string">
          <s:enumeration value="Default"/>
          <s:enumeration value="High"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ShipmentNotification">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Email" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="UseCompanyNameInFromLine" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="UseCompanyNameInSubject" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="NonDeliveryOption">
        <s:restriction base="s:string">
          <s:enumeration value="Undefined"/>
          <s:enumeration value="Return"/>
          <s:enumeration value="Abandon"/>
          <s:enumeration value="Redirect"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PaperSizeV1">
        <s:restriction base="s:string">
          <s:enumeration value="Default"/>
          <s:enumeration value="Letter85x11"/>
          <s:enumeration value="LabelSize"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="LabelRecipientInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="EmailAddress" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Note" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="CopyToOriginator" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ImageDpi">
        <s:restriction base="s:string">
          <s:enumeration value="ImageDpiDefault"/>
          <s:enumeration value="ImageDpi200"/>
          <s:enumeration value="ImageDpi300"/>
          <s:enumeration value="ImageDpi203"/>
          <s:enumeration value="ImageDpi96"/>
          <s:enumeration value="ImageDpi150"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ExtendedPostageInfoV1">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="eRefundAllowed" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="orderID" type="tns:string-0-100"/>
          <s:element minOccurs="0" maxOccurs="1" name="storeProfileName" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="storeProfileID" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="formattedOrderDetails" type="tns:string-0-8192"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="mediaID" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="printerID" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="netstampSerialNumber" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="mcatTracking" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="bridgeProfileType" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="clientVersion" type="tns:string-0-16"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="EnclosedServiceType">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="MarketingMail"/>
          <s:enumeration value="Periodicals"/>
          <s:enumeration value="NewsPeriodicals"/>
          <s:enumeration value="BoundMedia"/>
          <s:enumeration value="ParcelSelect"/>
          <s:enumeration value="OtherPackageService"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="EnclosedPackageType">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="Letters"/>
          <s:enumeration value="Flats"/>
          <s:enumeration value="Irregular Parcels"/>
          <s:enumeration value="Machinable Parcels"/>
          <s:enumeration value="Non-Machinable Parcels"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="OrderDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="OrderDate" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="OrderNumber" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShippingAndHandlingPrice" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="TotalDiscountAmount" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="TotalTaxAmount" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="OrderItems" type="tns:ArrayOfOrderItem"/>
          <s:element minOccurs="0" maxOccurs="1" name="OrderCurrency" type="tns:string-0-3"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfOrderItem">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="OrderItem" nillable="true" type="tns:OrderItem"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="OrderItem">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Quantity" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="ItemID" type="tns:string-0-25"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Description" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" name="UnitPrice" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="ImageUrl" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Options" type="tns:ArrayOfOption"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfOption">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Option" nillable="true" type="tns:Option"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Option">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Label" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateIndiciumResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxID" type="tns:string-1-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="TrackingNumber" type="tns:string-0-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="EncodedTrackingNumber" type="tns:string-0-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="BannerText" type="tns:string-0-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="TrailingSuperScript" type="tns:string-0-10"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnTrackingNumber" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
            <s:element minOccurs="1" maxOccurs="1" name="StampsTxID" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="URL" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
            <s:element minOccurs="1" maxOccurs="1" name="Mac" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageHash" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="ImageData" type="tns:ArrayOfBase64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="HoldForPickUpFacility" type="s2:HoldForPickUpFacility"/>
            <s:element minOccurs="0" maxOccurs="1" name="FormURL" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="labelCategory" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IndiciumData" type="s:base64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="LabelID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PostageBalance">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AvailablePostage" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="ControlTotal" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfBase64Binary">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="base64Binary" nillable="true" type="s:base64Binary"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateEnvelopeIndicium">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxID" type="tns:string-1-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnTo" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" name="PrintFromAddress" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="PrintToAddress" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="CustomerID" type="tns:string-1-64"/>
            <s:element minOccurs="0" maxOccurs="1" default="Normal" name="Mode" type="tns:CreateIndiciumModeV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="Auto" name="ImageType" type="tns:ImageType"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="CostCodeId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="HideFIM" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="RateToken" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="OrderId" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="memo" type="tns:string-0-150"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="BypassCleanseAddress" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="ImageId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="ImageId2" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnIndiciumData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ExtendedPostageInfo" type="tns:ExtendedPostageInfoV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnImageData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="ImageDpiDefault" name="ImageDpi" type="tns:ImageDpi"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="CreateIndiciumModeV1">
        <s:restriction base="s:string">
          <s:enumeration value="Normal"/>
          <s:enumeration value="Sample"/>
          <s:enumeration value="NoPostage"/>
          <s:enumeration value="Preview"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CreateEnvelopeIndiciumResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxID" type="tns:string-1-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="TrackingNumber" type="tns:string-0-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
            <s:element minOccurs="1" maxOccurs="1" name="StampsTxID" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="URL" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
            <s:element minOccurs="1" maxOccurs="1" name="Mac" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageHash" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IndiciumData" type="s:base64Binary"/>
            <s:element minOccurs="0" maxOccurs="1" name="ImageData" type="tns:ArrayOfBase64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateMailingLabelIndicia">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxId" type="tns:string-1-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="Layout" type="tns:string-0-128"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="PrintToAddress" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="StartRow" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="StartColumn" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="IndiciumInfo" type="tns:ArrayOfIndiciumInfoV39"/>
            <s:element minOccurs="0" maxOccurs="1" default="Normal" name="Mode" type="tns:CreateIndiciumModeV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="Auto" name="ImageType" type="tns:ImageType"/>
            <s:element minOccurs="0" maxOccurs="1" name="RateToken" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="memo" type="tns:string-0-150"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="BypassCleanseAddress" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnIndiciumData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ExtendedPostageInfo" type="tns:ExtendedPostageInfoV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="ImageId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="PrintFromAddress" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfIndiciumInfoV39">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="IndiciumInfoV39" nillable="true" type="tns:IndiciumInfoV39"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="IndiciumInfoV39">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
          <s:element minOccurs="0" maxOccurs="1" name="ReturnAddress" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="CostCodeId" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateMailingLabelIndiciaResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="StampsTxId" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="Url" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumbers" type="tns:ArrayOfString"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
            <s:element minOccurs="1" maxOccurs="1" name="IssuedLabelCount" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="ErrorReason" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Mac" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="PrintLayout" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IndiciumData" type="tns:ArrayOfBase64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateNetStampsIndicia">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxId" type="tns:string-1-128"/>
            <s:element minOccurs="0" maxOccurs="1" name="Layout" type="tns:string-0-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="NetStamps" type="tns:ArrayOfNetStampV42"/>
            <s:element minOccurs="0" maxOccurs="1" name="ReturnTo" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SampleOnly" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="Auto" name="ImageType" type="tns:ImageType"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="cost_code_id" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="ImageId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnIndiciaData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="RateToken" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="memo" type="tns:string-0-150"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="tns:string-0-100"/>
            <s:element minOccurs="0" maxOccurs="1" name="ExtendedPostageInfo" type="tns:ExtendedPostageInfoV1"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnImageData" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="ImageDpiDefault" name="ImageDpi" type="tns:ImageDpi"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfNetStampV42">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="NetStampV42" nillable="true" type="tns:NetStampV42"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="NetStampV42">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Row" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Column" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Rate" type="tns:RateV46"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateNetStampsIndiciaResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="IntegratorTxId" type="tns:string-1-128"/>
            <s:element minOccurs="1" maxOccurs="1" name="StampsTxId" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="URL" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
            <s:element minOccurs="1" maxOccurs="1" name="NetstampsStatus" type="tns:NetstampsStatus"/>
            <s:element minOccurs="1" maxOccurs="1" name="NetstampsIssued" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="ErrorReason" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Mac" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IndiciaData" type="tns:ArrayOfIndiciumData"/>
            <s:element minOccurs="0" maxOccurs="1" name="PrintLayout" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="ImageData" type="tns:ArrayOfBase64Binary"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="NetstampsStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Success"/>
          <s:enumeration value="PartialSuccess"/>
          <s:enumeration value="Failed"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfIndiciumData">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="IndiciumData" nillable="true" type="tns:IndiciumData"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="IndiciumData">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="IBI" type="s:base64Binary"/>
          <s:element minOccurs="1" maxOccurs="1" name="IBILite" type="s:base64Binary"/>
        </s:sequence>
      </s:complexType>
      <s:element name="RegisterAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IntegrationID" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="UserName" type="tns:string-0-40"/>
            <s:element minOccurs="1" maxOccurs="1" name="Password" type="tns:string-6-20"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword1Type" type="tns:CodewordType"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword2Type" type="tns:CodewordType"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword2" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PhysicalAddress" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" name="MailingAddress" type="tns:Address"/>
            <s:element minOccurs="1" maxOccurs="1" name="MachineInfo" type="tns:MachineInfo"/>
            <s:element minOccurs="1" maxOccurs="1" name="Email" type="tns:string-0-320"/>
            <s:element minOccurs="1" maxOccurs="1" name="AccountType" type="tns:AccountType"/>
            <s:element minOccurs="0" maxOccurs="1" name="PromoCode" type="tns:string-0-50"/>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="CreditCard" type="tns:CreditCard"/>
              <s:element minOccurs="0" maxOccurs="1" name="AchAccount" type="tns:AchAccount"/>
              <s:element minOccurs="0" maxOccurs="1" name="PPL" type="tns:PPLAccount"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ResetPasswordAfterRegistration" nillable="true" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="USD" name="UserCurrency" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IovationBlackBox" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="CodewordType">
        <s:restriction base="s:string">
          <s:enumeration value="Undefined"/>
          <s:enumeration value="MothersMaidenName"/>
          <s:enumeration value="PetsName"/>
          <s:enumeration value="BirthCity"/>
          <s:enumeration value="HighSchoolMascot"/>
          <s:enumeration value="FathersBirthplace"/>
          <s:enumeration value="StreetName"/>
          <s:enumeration value="FirstSchoolsName"/>
          <s:enumeration value="FirstCarsMakeModel"/>
          <s:enumeration value="Last4SocialSecurityNumber"/>
          <s:enumeration value="Last4DriversLicense"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="MachineInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="IPAddress" type="tns:string-0-15"/>
          <s:element minOccurs="0" maxOccurs="1" name="MacAddress" type="tns:string-0-12"/>
          <s:element minOccurs="0" maxOccurs="1" name="FP" type="tns:FP"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="FP">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="FP1" type="s:base64Binary"/>
          <s:element minOccurs="0" maxOccurs="1" name="FP2" type="s:base64Binary"/>
          <s:element minOccurs="0" maxOccurs="1" name="FP3" type="s:base64Binary"/>
          <s:element minOccurs="0" maxOccurs="1" name="FP4" type="s:base64Binary"/>
          <s:element minOccurs="0" maxOccurs="1" name="FP5" type="s:base64Binary"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AccountType">
        <s:restriction base="s:string">
          <s:enumeration value="Individual"/>
          <s:enumeration value="HomeOffice"/>
          <s:enumeration value="HomeBasedBusiness"/>
          <s:enumeration value="OfficeBasedBusiness"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="CreditCard">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CreditCardType" type="tns:CreditCardType"/>
          <s:element minOccurs="0" maxOccurs="1" name="AccountNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CVN" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ExpirationDate" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="BillingAddress" type="tns:Address"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="CreditCardType">
        <s:restriction base="s:string">
          <s:enumeration value="Visa"/>
          <s:enumeration value="MasterCard"/>
          <s:enumeration value="AmericanExpress"/>
          <s:enumeration value="Discover"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="AchAccount">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AchAccountType" type="tns:AchAccountType"/>
          <s:element minOccurs="0" maxOccurs="1" name="BankName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="AccountNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="RouteID" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="AccountHolderName" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AchAccountType">
        <s:restriction base="s:string">
          <s:enumeration value="Checking"/>
          <s:enumeration value="Savings"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="PPLAccount">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="PPLSessionRequestID" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="RegisterAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="RegistrationStatus" type="tns:RegistrationStatus"/>
            <s:element minOccurs="1" maxOccurs="1" name="SuggestedUserName" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="UserId" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="PromoUrl" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="RegistrationStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Success"/>
          <s:enumeration value="Fail"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="AddCarrier">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="UserOwnedAccount" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="Carrier" type="tns:Carrier"/>
            <s:element minOccurs="0" maxOccurs="1" name="AccountNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="AccountZIPCode" type="tns:string-0-10"/>
            <s:element minOccurs="0" maxOccurs="1" default="US" name="AccountCountry" type="tns:string-2-2"/>
            <s:element minOccurs="0" maxOccurs="1" name="Address" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="AgreeToEula" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="Invoice" type="tns:Invoice"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="NegotiatedRates" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="DeviceIdentity" type="tns:string-0-4000"/>
            <s:element minOccurs="0" maxOccurs="1" name="ClientId" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="ClientSecret" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="PickupNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="DistributionCenter" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Invoice">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ControlID" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="InvoiceNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="InvoiceAmount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="InvoiceDate" type="s:date"/>
        </s:sequence>
      </s:complexType>
      <s:element name="AddCarrierResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddImage">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ImageName" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ImageData" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="ImageCategory" nillable="true" type="tns:ImageCategory"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="ImageCategory">
        <s:restriction base="s:string">
          <s:enumeration value="LabelLogo"/>
          <s:enumeration value="BrandedTracking"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="AddImageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ImageId" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ImageUrl" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AddUserPaymentMethod">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="MachineInfo" type="tns:MachineInfo"/>
            <s:element minOccurs="0" maxOccurs="1" name="DefaultPaymentMethodType" nillable="true" type="tns:DefaultPaymentMethodTypeOptions"/>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="CreditCard" type="tns:CreditCard"/>
              <s:element minOccurs="0" maxOccurs="1" name="AchAccount" type="tns:AchAccount"/>
              <s:element minOccurs="0" maxOccurs="1" name="PPL" type="tns:PPLAccount"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="DefaultPaymentMethodTypeOptions">
        <s:restriction base="s:string">
          <s:enumeration value="AllDefaultPaymentMethods"/>
          <s:enumeration value="DefaultServiceFeePaymentMethod"/>
          <s:enumeration value="DefaultPostalPurchasePaymentMethod"/>
          <s:enumeration value="DefaultStorePaymentMethod"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="AddUserPaymentMethodResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PaymentMethodID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateBridgeAuthenticator">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="integrationID" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="batchAuthenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateBridgeAuthenticatorResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticated" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="AccountInfo" type="tns:UserInfo"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="UserInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="custId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="firstName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="lastName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="company" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="address1" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="address2" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="city" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="state" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="zip" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="phone" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="email" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Merchant" type="tns:Merchant"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="Merchant">
        <s:restriction base="s:string">
          <s:enumeration value="Stamps"/>
          <s:enumeration value="ShipWorks"/>
          <s:enumeration value="ShipStation"/>
          <s:enumeration value="Endicia"/>
          <s:enumeration value="GlobalPost"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="AuthenticateUser">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateUserResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="0001-01-01T00:00:00" name="LastLoginTime" type="s:dateTime"/>
            <s:element minOccurs="1" maxOccurs="1" name="ClearCredential" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="LoginBannerText" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PasswordExpired" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="CodewordsSet" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateWithTransferAuthenticator">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="integrationID" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="transferAuthenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AuthenticateWithTransferAuthenticatorResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelAccount">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:string-1-50"/>
            <s:element minOccurs="1" maxOccurs="1" name="State" type="tns:string-1-50"/>
            <s:element minOccurs="1" maxOccurs="1" name="City" type="tns:string-1-30"/>
            <s:element minOccurs="1" maxOccurs="1" name="Zip" type="tns:string-1-10"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SendEmail" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelCarrierPickup">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="USPS" name="Carrier" type="tns:Carrier"/>
            <s:element minOccurs="0" maxOccurs="1" default="Default" name="PickupType" type="tns:CarrierPickupType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="CarrierPickupType">
        <s:restriction base="s:string">
          <s:enumeration value="Default"/>
          <s:enumeration value="AutoPickup"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CancelCarrierPickupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickupStatus" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelIndicium">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:choice minOccurs="0" maxOccurs="unbounded">
              <s:element minOccurs="0" maxOccurs="1" name="TrackingNumbers" type="tns:ArrayOfString"/>
              <s:element minOccurs="0" maxOccurs="1" name="StampsTxIDs" type="tns:ArrayOfGuid"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfGuid">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="guid" type="s1:guid"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CancelIndiciumResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangeDefaultPaymentMethod">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="MachineInfo" type="tns:MachineInfo"/>
            <s:element minOccurs="0" maxOccurs="1" name="PaymentType" type="tns:DefaultPaymentMethodTypeOptions"/>
            <s:element minOccurs="0" maxOccurs="1" name="PaymentMethodID" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangeDefaultPaymentMethodResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePassword">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" default="" name="OldPassword" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="NewPassword" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePasswordResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePlan">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="PlanId" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="PromoCode" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePlanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PurchaseStatus" type="tns:PurchaseStatus"/>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionID" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="RejectionReason" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="PurchaseStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Pending"/>
          <s:enumeration value="Processing"/>
          <s:enumeration value="Success"/>
          <s:enumeration value="Rejected"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CheckCarrierPickupAvailability">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" default="0001-01-01" name="Date" type="s:date"/>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:CarrierPickupAddress"/>
            <s:element minOccurs="0" maxOccurs="1" default="USPS" name="Carrier" type="tns:Carrier"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="CarrierPickupAddress">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Company" type="tns:string-0-50"/>
          <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:string-1-50"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="SuiteOrApt" type="tns:string-0-35"/>
          <s:element minOccurs="1" maxOccurs="1" name="City" type="tns:string-1-35"/>
          <s:element minOccurs="1" maxOccurs="1" name="State" type="tns:string-2-2"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZIP" type="tns:string-5-5"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="ZIP4" type="tns:string-0-4"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CheckCarrierPickupAvailabilityResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickupDate" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickUpDayOfWeek" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="EstimatedAmount" type="s:decimal"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CleanseAddress">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="FromZIPCode" type="tns:string-0-5"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CleanseAddressResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:Address"/>
            <s:element minOccurs="1" maxOccurs="1" name="AddressMatch" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="CityStateZipOK" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="ResidentialDeliveryIndicator" type="tns:ResidentialDeliveryIndicatorType"/>
            <s:element minOccurs="0" maxOccurs="1" name="IsPOBox" nillable="true" type="s:boolean"/>
            <s:element minOccurs="1" maxOccurs="1" name="CandidateAddresses" type="tns:ArrayOfAddress"/>
            <s:element minOccurs="1" maxOccurs="1" name="StatusCodes" type="tns:StatusCodes"/>
            <s:element minOccurs="0" maxOccurs="1" name="Rates" type="tns:ArrayOfRateV46"/>
            <s:element minOccurs="0" maxOccurs="1" name="AddressCleansingResult" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="VerificationLevel" type="tns:AddressVerificationLevel"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="ResidentialDeliveryIndicatorType">
        <s:restriction base="s:string">
          <s:enumeration value="Yes"/>
          <s:enumeration value="No"/>
          <s:enumeration value="Unknown"/>
          <s:enumeration value="Unsupported"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfAddress">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Address" nillable="true" type="tns:Address"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="StatusCodes">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ReturnCode" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="Footnotes" type="tns:ArrayOfFootnote"/>
          <s:element minOccurs="0" maxOccurs="1" name="DpvFootnotes" type="tns:ArrayOfDpvFootnote"/>
          <s:element minOccurs="0" maxOccurs="1" name="SdcFootnotes" type="tns:ArrayOfInt"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfFootnote">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Footnote" nillable="true" type="tns:Footnote"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Footnote">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfDpvFootnote">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DpvFootnote" nillable="true" type="tns:DpvFootnote"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DpvFootnote">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfInt">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="int" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AddressVerificationLevel">
        <s:restriction base="s:string">
          <s:enumeration value="Partial"/>
          <s:enumeration value="Maximum"/>
          <s:enumeration value="None"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CreateBranding">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="BrandingProperties" type="tns:BrandingProperties"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SetAsDefault" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="BrandingProperties">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="" name="ReturnPolicy" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Email" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Phone" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="LogoTargetUrl" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="BrandName" type="tns:string-2-2147483647"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="LogoUrl" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="LogoType" type="tns:LogoType"/>
          <s:element minOccurs="0" maxOccurs="1" name="MenuLinks" type="tns:ArrayOfMenuLink"/>
          <s:element minOccurs="0" maxOccurs="1" name="SocialMedia" type="tns:ArrayOfSocialMedia"/>
          <s:element minOccurs="0" maxOccurs="1" name="Toggles" type="tns:Toggles"/>
          <s:element minOccurs="0" maxOccurs="1" name="Colors" type="tns:Colors"/>
          <s:element minOccurs="1" maxOccurs="1" name="Theme" type="tns:Theme"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="LogoType">
        <s:restriction base="s:string">
          <s:enumeration value="Image"/>
          <s:enumeration value="Text"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfMenuLink">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="MenuLink" nillable="true" type="tns:MenuLink"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="MenuLink">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Url" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Text" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSocialMedia">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="SocialMedia" nillable="true" type="tns:SocialMedia"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SocialMedia">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Type" type="tns:SocialMediaType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Url" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="SocialMediaType">
        <s:restriction base="s:string">
          <s:enumeration value="Twitter"/>
          <s:enumeration value="Instagram"/>
          <s:enumeration value="Youtube"/>
          <s:enumeration value="Pinterest"/>
          <s:enumeration value="Facebook"/>
          <s:enumeration value="Linkedin"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Toggles">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ShowCustomColors" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowSocialMedia" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowStoreUrl" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowMenuLinks" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowReturnPolicy" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowEmail" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowPhone" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowShipmentDetails" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowItemImages" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowShipmentPrices" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShowStoreAddress" nillable="true" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Colors">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Primary" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Secondary" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Tertiary" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="Theme">
        <s:restriction base="s:string">
          <s:enumeration value="Light"/>
          <s:enumeration value="Dark"/>
          <s:enumeration value="Peach"/>
          <s:enumeration value="Multicolor"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CreateBrandingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="BrandingId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateManifest">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="IntegratorTxID" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="StampsTxIds" type="tns:ArrayOfGuid"/>
            <s:element minOccurs="0" maxOccurs="1" name="TrackingNumbers" type="tns:ArrayOfString"/>
            <s:element minOccurs="0" maxOccurs="1" name="ShipDate" nillable="true" type="s:date"/>
            <s:element minOccurs="0" maxOccurs="1" default="Normal" name="PrintLayout" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="FromAddress" type="tns:Address"/>
            <s:element minOccurs="1" maxOccurs="1" name="ImageType" type="tns:ImageType"/>
            <s:element minOccurs="1" maxOccurs="1" name="PrintInstructions" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" default="ScanForm" name="ManifestType" type="tns:ManifestType"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfLabels" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="ManifestType">
        <s:restriction base="s:string">
          <s:enumeration value="ScanForm"/>
          <s:enumeration value="GlobalAdvantage"/>
          <s:enumeration value="GlobalPost"/>
          <s:enumeration value="DriverManifest"/>
          <s:enumeration value="All"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="CreateManifestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IntegratorTxID" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="EndOfDayManifests" type="tns:ArrayOfEndOfDayManifest"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfEndOfDayManifest">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="EndOfDayManifest" nillable="true" type="tns:EndOfDayManifest"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="EndOfDayManifest">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PickupCarrier" type="tns:PickupCarrier"/>
          <s:element minOccurs="1" maxOccurs="1" name="ManifestType" type="tns:ManifestType"/>
          <s:element minOccurs="0" maxOccurs="1" name="ManifestId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ManifestUrl" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ContainerLabels" type="tns:ArrayOfContainerLabelV1"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PickupCarrier">
        <s:restriction base="s:string">
          <s:enumeration value="None"/>
          <s:enumeration value="Usps"/>
          <s:enumeration value="Dhl"/>
          <s:enumeration value="Ups"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfContainerLabelV1">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ContainerLabelV1" nillable="true" type="tns:ContainerLabelV1"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ContainerLabelV1">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ContainerTrackingNumber" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ContainerUrl" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="StampsTxId" type="s1:guid"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateNotificationSetting">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="NotificationSettingProperties" type="tns:NotificationSettingProperties"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SetAsDefault" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="NotificationSettingProperties">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Triggers" type="tns:ArrayOfTrigger"/>
          <s:element minOccurs="0" maxOccurs="1" name="EmailConfig" type="tns:EmailConfig"/>
          <s:element minOccurs="0" maxOccurs="1" name="AllowSMSOptIn" nillable="true" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfTrigger">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Trigger" nillable="true" type="tns:Trigger"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Trigger">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="NotificationType" type="tns:NotificationType"/>
          <s:element minOccurs="0" maxOccurs="1" name="Channels" type="tns:ArrayOfChannel"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="NotificationType">
        <s:restriction base="s:string">
          <s:enumeration value="ShipmentCreated"/>
          <s:enumeration value="DeliveryDateAnnounced"/>
          <s:enumeration value="OutForDelivery"/>
          <s:enumeration value="Delivered"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfChannel">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Channel" nillable="true" type="tns:Channel"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Channel">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelType" type="tns:ChannelType"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ChannelType">
        <s:restriction base="s:string">
          <s:enumeration value="Email"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="EmailConfig">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ContentBlocks" type="tns:ArrayOfContentBlock"/>
          <s:element minOccurs="1" maxOccurs="1" name="FromName" type="tns:string-1-2147483647"/>
          <s:element minOccurs="0" maxOccurs="1" name="ReplyToEmail" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="BCCEmails" type="tns:ArrayOfBccEmail"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfContentBlock">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ContentBlock" nillable="true" type="tns:ContentBlock"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ContentBlock">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ContentBlockType" type="tns:ContentBlockType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="tns:string-1-2147483647"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ContentBlockType">
        <s:restriction base="s:string">
          <s:enumeration value="Alert"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfBccEmail">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="BccEmail" nillable="true" type="tns:BccEmail"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="BccEmail">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="EmailAddress" type="tns:string-1-2147483647"/>
        </s:sequence>
      </s:complexType>
      <s:element name="CreateNotificationSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="NotificationSettingId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateShipmentNotification">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="TrackingNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Carrier" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightLb" type="s:double"/>
            <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightOz" type="s:double"/>
            <s:element minOccurs="0" maxOccurs="1" name="TrackingType" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="BrandingId" nillable="true" type="s1:guid"/>
            <s:element minOccurs="0" maxOccurs="1" name="NotificationSettingId" nillable="true" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="EmailSenderName" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="From" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" name="To" type="tns:Address"/>
            <s:element minOccurs="0" maxOccurs="1" name="OrderDetails" type="tns:OrderDetails"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShipDate" type="s:dateTime"/>
            <s:element minOccurs="0" maxOccurs="1" default="true" name="SendEmail" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateShipmentNotificationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TrackingUrl" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteBranding">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="BrandingId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteBrandingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCarrier">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Carrier" type="tns:Carrier"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCarrierResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteImage">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ImageId" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteImageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNotificationSetting">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="NotificationSettingId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNotificationSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteUserPaymentMethod">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="PaymentMethodID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteUserPaymentMethodResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnumCodeWordTypes">
        <s:complexType/>
      </s:element>
      <s:element name="EnumCodeWordTypesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CodeWords" type="tns:ArrayOfCodeword"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCodeword">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CodewordV2" nillable="true" type="tns:Codeword"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Codeword">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CodewordType" type="tns:CodewordType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Value" type="s:unsignedInt"/>
          <s:element minOccurs="1" maxOccurs="1" name="Description" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="EnumCostCodes">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnumCostCodesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="CostCodes" type="tns:ArrayOfCost_code_info_v1"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCost_code_info_v1">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CostCodes" nillable="true" type="tns:cost_code_info_v1"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="cost_code_info_v1">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="id" type="s:unsignedInt"/>
          <s:element minOccurs="1" maxOccurs="1" name="name" type="tns:string-0-30"/>
        </s:sequence>
      </s:complexType>
      <s:element name="EnumNetStampsLayouts">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnumNetStampsLayoutsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Layouts" type="tns:ArrayOfNetStampsLayout"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfNetStampsLayout">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="NetStampsLayout" nillable="true" type="tns:NetStampsLayout"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="NetStampsLayout">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Description" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SerialNumberPattern" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="NumRows" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="NumColumns" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="BackgroundImageURL" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="BackgroundImageWidthPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="BackgroundImageHeightPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="EmptyNetStampImageURL" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="UsedNetStampImageURL" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="PrintedNetStampImageURL" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="NetStampImageWidthPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="NetStampImageHeightPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Row1StartsAtPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Column1StartsAtPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="HorizontalSpaceBetweenNetStampsPx" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="VerticalSpaceBetweenNetStampsPx" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="FinishAccountVerification">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="PhoneVerificationCode" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FinishAccountVerificationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FinishPasswordReset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Username" type="tns:string-1-40"/>
            <s:element minOccurs="1" maxOccurs="1" name="TempPassword" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="NewPassword" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="IntegrationId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FinishPasswordResetResponse">
        <s:complexType/>
      </s:element>
      <s:element name="GetAccountInfo">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccountInfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="AccountInfo" type="tns:AccountInfoV65"/>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:Address"/>
            <s:element minOccurs="1" maxOccurs="1" name="CustomerEmail" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="AccountStatus" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="DateAdvanceConfig" type="tns:DateAdvance"/>
            <s:element minOccurs="0" maxOccurs="1" name="VerificationPhoneNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="VerificationPhoneExtension" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="AccountInfoV65">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CustomerID" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="MeterNumber" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="UserID" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxPostageBalance" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="LPOCity" type="tns:string-0-29"/>
          <s:element minOccurs="1" maxOccurs="1" name="LPOState" type="tns:string-0-3"/>
          <s:element minOccurs="1" maxOccurs="1" name="LPOZip" type="tns:string-0-6"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="AccountId" type="s:long"/>
          <s:element minOccurs="1" maxOccurs="1" name="CorpID" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="StoreID" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="CostCodeLimit" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="MonthlyPostagePurchaseLimit" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxUsers" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Capabilities" type="tns:CapabilitiesV50"/>
          <s:element minOccurs="1" maxOccurs="1" name="MeterPhysicalAddress" type="tns:Address"/>
          <s:element minOccurs="1" maxOccurs="1" name="ResubmitStatus" type="tns:ResubmissionStatus"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="ResubmitCookie" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="PlanID" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="PendingPlanId" nillable="true" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Username" type="tns:string-0-40"/>
          <s:element minOccurs="0" maxOccurs="1" name="RatesetType" nillable="true" type="tns:RatesetType"/>
          <s:element minOccurs="0" maxOccurs="1" name="USPSRep" nillable="true" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="AutoBuySettings" type="tns:AutoBuySettings"/>
          <s:element minOccurs="0" maxOccurs="1" name="RateToken" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CustomerData" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Terms" type="tns:Terms"/>
          <s:element minOccurs="0" maxOccurs="1" name="OutstandingLabelBalance" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="MaxOutstandingLabelBalance" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="Merchant" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="MeterProvider" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxImageCount" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="SEApiToken" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="GAPickupCarrier" type="tns:PickupCarrier"/>
          <s:element minOccurs="1" maxOccurs="1" name="LocalCurrency" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="HasPOURMailerID" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="MaxParcelGuardInsuredValue" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="FacilityAssigned" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="ConfiguredCarriers" type="tns:ArrayOfConfiguredCarriers"/>
          <s:element minOccurs="1" maxOccurs="1" name="GPPickupCarrier" type="tns:PickupCarrier"/>
          <s:element minOccurs="1" maxOccurs="1" name="MailingZipCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="BalanceID" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="BrandedExternalPrints" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="CustomerGroup" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CapabilitiesV50">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV49">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPSPayOnUse" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV49">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV48">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPSGroundSaver" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV48">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV47">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="BlockUSPSPrint" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV47">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV46">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="BrandedExternalPrints" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV46">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV45">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPSPayOnUseReturns" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPSReturns" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPSStandalonePayOnUseReturns" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV45">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV44">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUSPSReturnServicePrepaid" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV44">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV43">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="BrandedTrackingSMSSupport" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV43">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV42">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUsePPL" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV42">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV41">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintDHLEcommerce" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV41">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV40">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUSPSReturnService" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV40">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV39">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanExportPrintHistory" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanScheduleReports" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseRateAdvisor" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseBrandedEmails" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV39">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV38">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseAutomationRules" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseAdvancedAutomationRules" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseFBA" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV38">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV37">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseBrandedTracking" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV37">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV36">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseAutoPickupUSPS" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV36">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV35">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintInstaLabel" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV35">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV34">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintCanadaPost" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV34">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV33">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPurchaseUSPSInsurance" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV33">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV32">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGPDomestic" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV32">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV31">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="DisplayFXByDefault" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="CanPrintFXDirect" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="CanPrintFXIntl" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="DisplayDXByDefault" nillable="true" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV31">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV30">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="CanRateShop" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="DisplayUPSByDefault" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="CanPrintUPSDirect" nillable="true" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" name="CanPrintUPSIntl" nillable="true" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV30">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV29">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUPS" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV29">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV28">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintFCMIFlatDelCon" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanBypassDomesticCustoms" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV28">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV27">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUnfundedIndicium" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV27">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV26">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="AlwaysIncludeDefaultImageOnShippingLabels" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV26">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV25">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPMOD" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPMEOD" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintRetailGround" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPSLW" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZPB" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV25">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV24">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="PostalOverride" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="GlobalPostPostal" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPurchaseParcelGuardInsurance" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV24">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV23">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGlobalPostPlus" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV23">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV22">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="BlockSLAFromServiceBanner" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintMailingLabel" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="IsManualManifesting" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="AllowNineDigitBarCode" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV22">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV21">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintUSPSReturn" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintStandaloneUSPSReturn" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV21">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV20">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintDeliveredDutyPaid" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV20">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV19">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseMCW" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV19">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV18">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintFX" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintFXPayOnUse" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV18">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV17">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintFCPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPSPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintIntlPresortSinglePiece" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToFCIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToPMIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToPMEIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToFCPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToPSPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanConvertToIntlPresortSinglePiece" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV17">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV16">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="EnableExProShipperODBC" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV16">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV15">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintFCIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPMIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPMEIPresort" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintPresortPayOnUse" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanChangePaymentType" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV15">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV14">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintDX" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintDXPayOnUse" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV14">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV13">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="DisableLabelLogo" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV13">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV12">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanCleanseIntlAddress" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanBypassCleanseAddress" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV12">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV11">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGPPayOnUse" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGPSmartSaverPayOnUse" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV11">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV10">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGPSmartSaver" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV10">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV9">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanCreateUnlimitedStores" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV9">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV8">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintGP" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="GPShipToConsolidator" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV8">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV7">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="AllowUspsMPosLabel" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="DisableConversationToken" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintCubic" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV7">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV6">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseCertifiedMail" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintAllIndiciumValues" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV6">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV5">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanUseInvoicing" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV5">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV4">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanCreateCriticalMail" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV4">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV3">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="IsIBIPEnabled" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV3">
        <s:complexContent mixed="false">
          <s:extension base="tns:CapabilitiesV2">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintReturnShippingLabel" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanManageUsers" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPrintNetStamps" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanEmailNotifications" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanViewReports" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanCreateSCANForm" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="AllowRestrictedSheets" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="HideUnavailableFeatures" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="WebPostage" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanViewInsuranceHistory" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanChangeServicePlan" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="HideEstimatedDeliveryTime" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanPurchaseFromStore" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanChangePhysicalAddress" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanChangePaymentMethod" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanChangeContactInfo" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="CanViewAdvancedReporting" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CapabilitiesV2">
        <s:complexContent mixed="false">
          <s:extension base="tns:Capabilities">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="AllowAllMailClasses" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="Capabilities">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CanPrintShipping" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanUseCostCodes" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanUseHiddenPostage" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanPurchaseSDCInsurance" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanPrintMemoOnShippingLabel" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanPrintInternational" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanPurchasePostage" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanEditCostCodes" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="MustUseCostCodes" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="CanViewOnlineReports" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="PerPrintLimit" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ResubmissionStatus">
        <s:restriction base="s:string">
          <s:enumeration value="NOT_IN_RESUBMISSION"/>
          <s:enumeration value="PAYMENT_RESUBMISSION"/>
          <s:enumeration value="NON_CORRECTABLE_PAYMENT_RESUBMISSION"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="RatesetType">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="Retail"/>
          <s:enumeration value="CBP"/>
          <s:enumeration value="CPP"/>
          <s:enumeration value="NSA"/>
          <s:enumeration value="STMP"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="AutoBuySettings">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AutoBuyEnabled" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="PurchaseAmount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="TriggerAmount" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Terms">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="TermsGP" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsAR" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsSL" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsDX" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsFX" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsUPS" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsCM" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsCanadaPost" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="TermsParcelGuard" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfConfiguredCarriers">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ConfiguredCarrier" nillable="true" type="tns:ConfiguredCarriers"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ConfiguredCarriers">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Carrier" type="tns:Carrier"/>
          <s:element minOccurs="1" maxOccurs="1" name="UserOwnedAccount" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DateAdvance">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="MaxDateAdvanceEnvelope" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxDateAdvanceMailingLabel" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="MaxDateAdvanceShippingLabel" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetBalanceHistory">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="TransactionsPerPage" nillable="true" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionDateMin" type="s:date"/>
            <s:element minOccurs="0" maxOccurs="1" name="TransactionDateMax" nillable="true" type="s:date"/>
            <s:element minOccurs="1" maxOccurs="1" name="Filters" type="tns:ArrayOfTransactionType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfTransactionType">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="TransactionType" type="tns:TransactionType"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="TransactionType">
        <s:restriction base="s:string">
          <s:enumeration value="All"/>
          <s:enumeration value="Purchase"/>
          <s:enumeration value="Credit"/>
          <s:enumeration value="Print"/>
          <s:enumeration value="Adjustment"/>
          <s:enumeration value="Refund"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetBalanceHistoryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="BalanceHistoryToken" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TotalTransactions" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="TotalPages" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Transactions" type="tns:ArrayOfTransaction"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfTransaction">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Transaction" nillable="true" type="tns:Transaction"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Transaction">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Date" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Cost" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="Credit" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="Balance" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="Type" type="tns:TransactionType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Brand" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PurchaseType" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetBalanceHistoryByToken">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="BalanceHistoryToken" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="1" name="PageNumber" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetBalanceHistoryByTokenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="BalanceHistoryToken" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionsPerPage" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="TotalTransactions" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Transactions" type="tns:ArrayOfTransaction"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetBranding">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetBrandingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Brandings" type="tns:ArrayOfBranding"/>
            <s:element minOccurs="1" maxOccurs="1" name="defaultBrandingId" nillable="true" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfBranding">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Branding" nillable="true" type="tns:Branding"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Branding">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="BrandingId" nillable="true" type="s1:guid"/>
          <s:element minOccurs="0" maxOccurs="1" name="BrandingProperties" type="tns:BrandingProperties"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetCarrierPickupList">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCarrierPickupListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Pickups" type="tns:ArrayOfCarrierPickupInformation"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCarrierPickupInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CarrierPickupInformation" nillable="true" type="tns:CarrierPickupInformation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CarrierPickupInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumber" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Carrier" type="tns:Carrier"/>
          <s:element minOccurs="1" maxOccurs="1" name="PickupDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="PickUpDayOfWeek" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ContactInformation" type="tns:CarrierPickupContactInformationV2"/>
          <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:CarrierPickupAddressV2"/>
          <s:element minOccurs="1" maxOccurs="1" name="PackageInformation" type="tns:CarrierPickupPackageInformation"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeEarliest" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeLatest" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="EstimatedAmount" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="PickupType" type="tns:CarrierPickupType"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CarrierPickupContactInformationV2">
        <s:complexContent mixed="false">
          <s:extension base="tns:CarrierPickupContactInformation">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" default="" name="Email" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CarrierPickupContactInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="FirstName" type="tns:string-1-50"/>
          <s:element minOccurs="1" maxOccurs="1" name="LastName" type="tns:string-1-50"/>
          <s:element minOccurs="1" maxOccurs="1" name="PhoneNumber" type="tns:string-10-10"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="PhoneExt" type="tns:string-0-6"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CarrierPickupAddressV2">
        <s:complexContent mixed="false">
          <s:extension base="tns:CarrierPickupAddress">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" default="" name="Address2" type="tns:string-0-50"/>
              <s:element minOccurs="0" maxOccurs="1" default="" name="Address3" type="tns:string-0-50"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="CarrierPickupPackageInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfExpressMailPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfPriorityMailPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfInternationalPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfFirstClassPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfParcelSelectPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="NumberOfOtherPieces" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="TotalWeightOfPackagesLbs" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="PackageLocation" type="tns:CarrierPickupLocationV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="SpecialInstruction" type="tns:string-0-75"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="CarrierPickupLocationV1">
        <s:restriction base="s:string">
          <s:enumeration value="FrontDoor"/>
          <s:enumeration value="BackDoor"/>
          <s:enumeration value="SideDoor"/>
          <s:enumeration value="KnockOnDoorOrRingBell"/>
          <s:enumeration value="MailRoom"/>
          <s:enumeration value="Office"/>
          <s:enumeration value="Reception"/>
          <s:enumeration value="InOrAtMailbox"/>
          <s:enumeration value="Other"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetChangePlanStatus">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionID" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetChangePlanStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PurchaseStatus" type="tns:PurchaseStatus"/>
            <s:element minOccurs="0" maxOccurs="1" name="RejectionReason" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCodewordQuestions">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Username" type="tns:string-0-40"/>
            <s:element minOccurs="0" maxOccurs="1" name="IntegrationId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCodewordQuestionsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Codeword1Question" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Codeword2Question" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetImageList">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetImageListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="Images" type="tns:ArrayOfImage"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfImage">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Image" nillable="true" type="tns:Image"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Image">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ImageId" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageUrl" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageCategory" type="tns:ImageCategory"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetNetStampsImages">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNetStampsImagesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="NetStampsImages" type="tns:ArrayOfNetStampsImage"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfNetStampsImage">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="NetStampsImage" nillable="true" type="tns:NetStampsImage"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="NetStampsImage">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ImageName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageID" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageCategory" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageDescription" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageUrl" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageIndex" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="ImageType" type="tns:NetStampsImageType"/>
          <s:element minOccurs="1" maxOccurs="1" name="PreviewOnly" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="PlansUpgradeToPrintImage" type="tns:ArrayOfPlan"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="NetStampsImageType">
        <s:restriction base="s:string">
          <s:enumeration value="Base"/>
          <s:enumeration value="Special"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfPlan">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Plan" nillable="true" type="tns:Plan"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Plan">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PlanId" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="PlanName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="MonthlyBaseFee" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetNotificationSettings">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNotificationSettingsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="DefaultNotificationSettingId" nillable="true" type="s1:guid"/>
            <s:element minOccurs="1" maxOccurs="1" name="NotificationSettings" type="tns:ArrayOfNotificationSetting"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfNotificationSetting">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="NotificationSetting" nillable="true" type="tns:NotificationSetting"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="NotificationSetting">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="NotificationSettingId" type="s1:guid"/>
          <s:element minOccurs="0" maxOccurs="1" name="NotificationSettingProperties" type="tns:NotificationSettingProperties"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetShipmentList">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" default="50" name="ShipmentsPerPage" type="tns:integer-ge-1-le-500"/>
            <s:element minOccurs="1" maxOccurs="1" name="Filters" type="tns:Filters"/>
            <s:element minOccurs="0" maxOccurs="1" name="IncludeFields" type="tns:ArrayOfIncludeField"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Filters">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PrintDateMin" nillable="true" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="PrintDateMax" nillable="true" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="SearchCriteria" type="tns:ArrayOfSearchFor"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSearchFor">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="SearchFor" type="tns:SearchFor"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="SearchFor">
        <s:restriction base="s:string">
          <s:enumeration value="All"/>
          <s:enumeration value="Refund"/>
          <s:enumeration value="Adjustments"/>
          <s:enumeration value="ClaimEligibleLostOrDamaged"/>
          <s:enumeration value="ClaimIneligible"/>
          <s:enumeration value="ClaimReceived"/>
          <s:enumeration value="ClaimPendingReview"/>
          <s:enumeration value="ClaimPendingAdditionalInfo"/>
          <s:enumeration value="ClaimApproved"/>
          <s:enumeration value="ClaimRejected"/>
          <s:enumeration value="OutstandingLabels"/>
          <s:enumeration value="PayOnUse"/>
          <s:enumeration value="DHLExpress"/>
          <s:enumeration value="FedEx"/>
          <s:enumeration value="PayOnUseReturn"/>
          <s:enumeration value="ClaimPendingRecipientVerification"/>
          <s:enumeration value="ClaimEligibleDamaged"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfIncludeField">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="IncludeField" type="tns:IncludeField"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="IncludeField">
        <s:restriction base="s:string">
          <s:enumeration value="StampsTxId"/>
          <s:enumeration value="ShipmentStatus"/>
          <s:enumeration value="PrintDate"/>
          <s:enumeration value="MailDate"/>
          <s:enumeration value="DeliveredDate"/>
          <s:enumeration value="TrackingNumber"/>
          <s:enumeration value="ScanFormId"/>
          <s:enumeration value="Service"/>
          <s:enumeration value="AddOns"/>
          <s:enumeration value="FromZIPCode"/>
          <s:enumeration value="ReturnTo"/>
          <s:enumeration value="ShipTo"/>
          <s:enumeration value="PackageInfo"/>
          <s:enumeration value="References"/>
          <s:enumeration value="RefundType"/>
          <s:enumeration value="RefundStatus"/>
          <s:enumeration value="ExpiryDate"/>
          <s:enumeration value="Reserved"/>
          <s:enumeration value="AdjustmentDetails"/>
          <s:enumeration value="ClaimDetails"/>
          <s:enumeration value="ReturnTrackingNumber"/>
          <s:enumeration value="Surcharges"/>
          <s:enumeration value="LabelCategory"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetShipmentListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShipmentListToken" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TotalPages" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Shipments" type="tns:ArrayOfShipmentV39"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfShipmentV39">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ShipmentV39" nillable="true" type="tns:ShipmentV39"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ShipmentV39">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="StampsTxId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShipmentStatus" nillable="true" type="tns:ShipmentStatusV2"/>
          <s:element minOccurs="0" maxOccurs="1" name="PrintDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="MailDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="DeliveredDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="TrackingNumber" type="tns:string-1-128"/>
          <s:element minOccurs="0" maxOccurs="1" name="ReturnTrackingNumber" type="tns:string-1-128"/>
          <s:element minOccurs="0" maxOccurs="1" name="ScanFormId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Service" type="tns:ServicesV22"/>
          <s:element minOccurs="0" maxOccurs="1" name="AddOns" type="tns:ArrayOfAddOnV20"/>
          <s:element minOccurs="0" maxOccurs="1" name="FromZIPCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ReturnTo" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" name="ShipTo" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" name="PackageInfo" type="tns:PackageInfoV6"/>
          <s:element minOccurs="0" maxOccurs="1" name="References" type="tns:ReferencesV3"/>
          <s:element minOccurs="0" maxOccurs="1" name="RefundType" nillable="true" type="tns:RefundTypeV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="RefundStatus" type="tns:RefundStatusV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="ExpiryDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reserved" type="tns:ServicesV22"/>
          <s:element minOccurs="0" maxOccurs="1" name="AdjustmentDetails" type="tns:AdjustmentDetails"/>
          <s:element minOccurs="0" maxOccurs="1" name="ClaimDetails" type="tns:PGClaimDetails"/>
          <s:element minOccurs="0" maxOccurs="1" name="Surcharges" type="tns:ArrayOfSurchargeV5"/>
          <s:element minOccurs="0" maxOccurs="1" name="LabelCategory" nillable="true" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ShipmentStatusV2">
        <s:restriction base="s:string">
          <s:enumeration value="Printed"/>
          <s:enumeration value="In Transit"/>
          <s:enumeration value="Received at Opening Unit"/>
          <s:enumeration value="Delivered"/>
          <s:enumeration value="Pending Refund"/>
          <s:enumeration value="Refunded"/>
          <s:enumeration value="Voided"/>
          <s:enumeration value="Unprinted"/>
          <s:enumeration value="Expired"/>
          <s:enumeration value="Recipient Action Required"/>
          <s:enumeration value="Action Required"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ServicesV22">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ServiceType" type="tns:ServiceType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Amount" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="CubicPricingTier" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsMetroRate" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Carrier" type="tns:Carrier"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="PackageInfoV6">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PackageType" type="tns:PackageTypeV11"/>
          <s:element minOccurs="1" maxOccurs="1" name="NumberOfLabels" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightLb" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="WeightOz" type="s:double"/>
          <s:element minOccurs="0" maxOccurs="1" name="EntryFacility" nillable="true" type="tns:EntryFacilityV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="SortType" nillable="true" type="tns:SortTypeV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="EnclosedServiceType" nillable="true" type="tns:EnclosedServiceType"/>
          <s:element minOccurs="0" maxOccurs="1" name="EnclosedPackageType" nillable="true" type="tns:EnclosedPackageType"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ReferencesV3">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="UserName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CostCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Memo" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="OrderId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference1" type="tns:string-0-100"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference2" type="tns:string-0-100"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference3" type="tns:string-0-100"/>
          <s:element minOccurs="0" maxOccurs="1" name="Reference4" type="tns:string-0-100"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="RefundTypeV1">
        <s:restriction base="s:string">
          <s:enumeration value="E-refund"/>
          <s:enumeration value="Mail-in"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="RefundStatusV1">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PostageRefundId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ClaimNumber" nillable="true" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="RequestDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="PostageRefundStatus" type="tns:PostageRefundStatusV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="EstimatedPostageRefundCompletionDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="PostageAmountRequested" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="PostageAmountApproved" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="PostageRefundResult" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="InsuranceRefundStatus" nillable="true" type="tns:PostageRefundStatusV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="EstimatedInsuranceRefundCompletionDate" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="InsuranceAmountRequested" nillable="true" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" name="InsuranceAmountApproved" nillable="true" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PostageRefundStatusV1">
        <s:restriction base="s:string">
          <s:enumeration value="Pending"/>
          <s:enumeration value="Processing"/>
          <s:enumeration value="Complete"/>
          <s:enumeration value="Duplicate"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="AdjustmentDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AdjustmentStatus" type="tns:AdjustmentStatus"/>
          <s:element minOccurs="0" maxOccurs="1" name="DateAdjustmentProcessed" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="0" maxOccurs="1" name="DateRecorded" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="ActualTrackingNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualFromZIPCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualToCountry" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualToZip" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ActualService" type="tns:ActualService"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualAddOns" type="tns:ArrayOfAddOnV20"/>
          <s:element minOccurs="1" maxOccurs="1" name="ActualPackageInfo" type="tns:ActualPackageInfo"/>
          <s:element minOccurs="0" maxOccurs="1" name="Dispute" type="tns:DisputeInfoV1"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualFromCountry" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualFromCity" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualToCity" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ActualSurcharges" type="tns:ArrayOfSurchargeV5"/>
          <s:element minOccurs="0" maxOccurs="1" name="OtherSurchargeAmounts" nillable="true" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AdjustmentStatus">
        <s:restriction base="s:string">
          <s:enumeration value="NoAdjustment"/>
          <s:enumeration value="Processed"/>
          <s:enumeration value="Refunded"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ActualService">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ActualServiceType" type="tns:ServiceType"/>
          <s:element minOccurs="1" maxOccurs="1" name="AdjustedAmount" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ActualPackageInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ActualPackageType" type="tns:PackageTypeV11"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="ActualWeight" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="ActualLength" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="ActualWidth" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="ActualHeight" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DisputeInfoV1">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DisputeStatus" type="tns:DisputeStatus"/>
          <s:element minOccurs="1" maxOccurs="1" name="DisputeCreateDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="DisputeLastUpdateDate" type="s:dateTime"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="DisputeStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Pending"/>
          <s:enumeration value="Approved"/>
          <s:enumeration value="Rejected"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="PGClaimDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ClaimType" type="tns:PGClaimType"/>
          <s:element minOccurs="1" maxOccurs="1" name="AmountRequested" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="AmountApproved" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="EventDate" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Status" type="tns:PGClaimStatus"/>
          <s:element minOccurs="0" maxOccurs="1" name="StatusDetail" nillable="true" type="tns:PGClaimSubStatus"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PGClaimType">
        <s:restriction base="s:string">
          <s:enumeration value="Damaged"/>
          <s:enumeration value="Lost"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PGClaimStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="Received"/>
          <s:enumeration value="PendingRecipientVerification"/>
          <s:enumeration value="PendingAdditionalInfo"/>
          <s:enumeration value="PendingReview"/>
          <s:enumeration value="Approved"/>
          <s:enumeration value="Closed"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PGClaimSubStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="RecipientDeniedClaim"/>
          <s:enumeration value="InvalidProofOfValue"/>
          <s:enumeration value="InvalidProofOfDamage"/>
          <s:enumeration value="InvalidProofOfLoss"/>
          <s:enumeration value="InvalidProofOfValueAndDamage"/>
          <s:enumeration value="InvalidProofOfValueAndLoss"/>
          <s:enumeration value="Other"/>
          <s:enumeration value="Auto"/>
          <s:enumeration value="Manual"/>
          <s:enumeration value="Paid"/>
          <s:enumeration value="PendingPayment"/>
          <s:enumeration value="ClaimExpired"/>
          <s:enumeration value="ByCustomer"/>
          <s:enumeration value="ByAgent"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetShipmentListByToken">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ShipmentListToken" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PageNumber" type="s:int"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetShipmentListByTokenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShipmentListToken" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShipmentsPerPage" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="TotalPages" type="s:int"/>
            <s:element minOccurs="0" maxOccurs="1" name="Shipments" type="tns:ArrayOfShipmentV39"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupportedCountries">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSupportedCountriesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="CountryInfo" type="tns:ArrayOfCountryInfo"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfCountryInfo">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CountryInfo" nillable="true" type="tns:CountryInfo"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="CountryInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsoCode" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="GetURL">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="URLType" type="tns:UrlType"/>
            <s:element minOccurs="1" maxOccurs="1" name="ApplicationContext" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="UrlType">
        <s:restriction base="s:string">
          <s:enumeration value="HomePage"/>
          <s:enumeration value="AccountSettingsPage"/>
          <s:enumeration value="EditCostCodesPage"/>
          <s:enumeration value="OnlineReportsPage"/>
          <s:enumeration value="HelpPage"/>
          <s:enumeration value="OnlineReportingHistory"/>
          <s:enumeration value="OnlineReportingRefund"/>
          <s:enumeration value="OnlineReportingPickup"/>
          <s:enumeration value="OnlineReportingSCAN"/>
          <s:enumeration value="OnlineReportingClaim"/>
          <s:enumeration value="StoreChangePlan"/>
          <s:enumeration value="WebClientHome"/>
          <s:enumeration value="ReportsBalances"/>
          <s:enumeration value="ReportsExpenses"/>
          <s:enumeration value="ReportsPrints"/>
          <s:enumeration value="StoreBuyPostage"/>
          <s:enumeration value="StoreMeters"/>
          <s:enumeration value="StoreUsers"/>
          <s:enumeration value="StorePaymentMethods"/>
          <s:enumeration value="StoreCorpContactInfo"/>
          <s:enumeration value="StoreMeterUsers"/>
          <s:enumeration value="StoreMeterSettings"/>
          <s:enumeration value="StoreMeterAddress"/>
          <s:enumeration value="StoreShippingAddresses"/>
          <s:enumeration value="StoreReferAFriend"/>
          <s:enumeration value="StoreAccountCredit"/>
          <s:enumeration value="StoreReorder"/>
          <s:enumeration value="StoreMyProfile"/>
          <s:enumeration value="StorePassword"/>
          <s:enumeration value="StoreCommPreferences"/>
          <s:enumeration value="StoreNetStampsLabels"/>
          <s:enumeration value="StoreShippingLabels"/>
          <s:enumeration value="StoreMailingLabels"/>
          <s:enumeration value="StoreScalesAndPrinters"/>
          <s:enumeration value="StoreFreeUSPSSupplies"/>
          <s:enumeration value="StoreBubbleMailers"/>
          <s:enumeration value="StoreShippingSupplies"/>
          <s:enumeration value="StoreScales"/>
          <s:enumeration value="StoreAveryNetStampsLabels"/>
          <s:enumeration value="StoreAveryMailingLabels"/>
          <s:enumeration value="StoreMeterContactInfo"/>
          <s:enumeration value="StoreEditMeterAddress"/>
          <s:enumeration value="StoreHome"/>
          <s:enumeration value="StoreAccount"/>
          <s:enumeration value="StoreCostCode"/>
          <s:enumeration value="StoreHistory"/>
          <s:enumeration value="StoreFaq"/>
          <s:enumeration value="StoreCustomerHome"/>
          <s:enumeration value="StoreGetAccountInfoJSon"/>
          <s:enumeration value="StoreSetAccountInfoJSon"/>
          <s:enumeration value="StoreUserCategories"/>
          <s:enumeration value="StoreCategory"/>
          <s:enumeration value="StoreUpdatePaymentMethodResubmit"/>
          <s:enumeration value="StoreDefaultPaymentMethods"/>
          <s:enumeration value="StoreSignOut"/>
          <s:enumeration value="CustomShipNotifyEmail"/>
          <s:enumeration value="HelpCenterHome"/>
          <s:enumeration value="MustChangePassword"/>
          <s:enumeration value="Contacts"/>
          <s:enumeration value="WebClientPreferences"/>
          <s:enumeration value="WebClientDefault"/>
          <s:enumeration value="StoreAutoBuy"/>
          <s:enumeration value="SetTermsGeneral"/>
          <s:enumeration value="UploadImage"/>
          <s:enumeration value="EndiciaLogin"/>
          <s:enumeration value="PhoneVerification"/>
          <s:enumeration value="CarrierSetupDHLExpress"/>
          <s:enumeration value="CarrierSetupUPS"/>
          <s:enumeration value="CarrierManagement"/>
          <s:enumeration value="StoreOrderHistory"/>
          <s:enumeration value="StoreSubscriptions"/>
          <s:enumeration value="StoreHardware"/>
          <s:enumeration value="StoreEnvelopes"/>
          <s:enumeration value="StoreLabels"/>
          <s:enumeration value="StoreClearNetStamps"/>
          <s:enumeration value="StoreOriginalNetStamps"/>
          <s:enumeration value="StoreNetStamps"/>
          <s:enumeration value="StoreSignIn"/>
          <s:enumeration value="AddPayment"/>
          <s:enumeration value="CarrierSetupFedEx"/>
          <s:enumeration value="ParcelGuard"/>
          <s:enumeration value="BrandedTracking"/>
          <s:enumeration value="RateAdvisor"/>
          <s:enumeration value="PPL"/>
          <s:enumeration value="ParcelGuardClaim"/>
          <s:enumeration value="ParcelGuardClaimStatus"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="GetURLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="URL" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="PPLSessionRequestID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListPaymentMethods">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="PaymentMethodID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ListPaymentMethodsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PaymentMethods" type="tns:ArrayOfPaymentMethod"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfPaymentMethod">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="PaymentMethod" nillable="true" type="tns:PaymentMethod"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="PaymentMethod">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DefaultPayment" type="tns:ArrayOfDefaultPaymentMethodTypeOptions"/>
          <s:element minOccurs="0" maxOccurs="1" name="BillingMethod" type="tns:BillingMethod"/>
          <s:element minOccurs="0" maxOccurs="1" name="BankName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CreditCardType" nillable="true" type="tns:CreditCardType"/>
          <s:element minOccurs="0" maxOccurs="1" name="Last4" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PaymentMethodId" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfDefaultPaymentMethodTypeOptions">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DefaultPaymentMethodTypeOptions" type="tns:DefaultPaymentMethodTypeOptions"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="BillingMethod">
        <s:restriction base="s:string">
          <s:enumeration value="ACH"/>
          <s:enumeration value="CreditCard"/>
          <s:enumeration value="OMAS"/>
          <s:enumeration value="Invoice"/>
          <s:enumeration value="EnterprisePaymentAccount"/>
          <s:enumeration value="PPL"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="ModifyBranding">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Branding" type="tns:Branding"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SetAsDefault" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ModifyBrandingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ModifyCarrierPickup">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="ContactInformation" type="tns:CarrierPickupContactInformation"/>
            <s:element minOccurs="0" maxOccurs="1" name="PackageInformation" type="tns:CarrierPickupPackageInformation"/>
            <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="USPS" name="Carrier" type="tns:Carrier"/>
            <s:element minOccurs="0" maxOccurs="1" default="Default" name="PickupType" type="tns:CarrierPickupType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ModifyCarrierPickupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickupDate" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickUpDayOfWeek" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumber" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickupStatus" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ModifyNotificationSetting">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="NotificationSetting" type="tns:NotificationSetting"/>
            <s:element minOccurs="0" maxOccurs="1" default="false" name="SetAsDefault" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ModifyNotificationSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PlaceOrder">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Skus" type="tns:ArrayOfSku"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PromoCode" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShippingAddress" type="tns:Address"/>
            <s:element minOccurs="1" maxOccurs="1" name="StoreShippingMethod" type="tns:StoreShippingMethodType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfSku">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Sku" nillable="true" type="tns:Sku"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Sku">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Id" type="tns:string-1-100"/>
          <s:element minOccurs="0" maxOccurs="1" default="0" name="Quantity" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="SkuSubTotal" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="StoreShippingMethodType">
        <s:restriction base="s:string">
          <s:enumeration value="Basic"/>
          <s:enumeration value="Normal"/>
          <s:enumeration value="Expedited"/>
          <s:enumeration value="Rush"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="PlaceOrderResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="StoreOrderId" type="tns:string-0-50"/>
            <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreOrderTotal" type="s:decimal"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PriceOrder">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="Skus" type="tns:ArrayOfSku"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PromoCode" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ShippingAddress" type="tns:Address"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PriceOrderResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="StoreShippingMethodOptions" type="tns:ArrayOfStoreShippingMethodOption"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfStoreShippingMethodOption">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="StoreShippingMethodOption" nillable="true" type="tns:StoreShippingMethodOption"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="StoreShippingMethodOption">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="StoreShippingMethod" type="tns:StoreShippingMethodType"/>
          <s:element minOccurs="1" maxOccurs="1" name="StoreDeliveryTimeMinimum" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="StoreDeliveryTimeMaximum" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Skus" type="tns:ArrayOfSku"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreProductTotal" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreProductDiscount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreShippingAmount" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreTaxTotal" type="s:decimal"/>
          <s:element minOccurs="0" maxOccurs="1" default="0.0" name="StoreOrderTotal" type="s:decimal"/>
        </s:sequence>
      </s:complexType>
      <s:element name="PurchasePostage">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="PurchaseAmount" type="s:decimal"/>
            <s:element minOccurs="1" maxOccurs="1" name="ControlTotal" type="s:decimal"/>
            <s:element minOccurs="0" maxOccurs="1" name="MI" type="tns:MachineInfo"/>
            <s:element minOccurs="0" maxOccurs="1" name="IntegratorTxID" type="tns:string-1-128"/>
            <s:element minOccurs="0" maxOccurs="1" name="SendEmail" nillable="true" type="s:boolean"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PurchasePostageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PurchaseStatus" type="tns:PurchaseStatus"/>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionID" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="PostageBalance" type="tns:PostageBalance"/>
            <s:element minOccurs="0" maxOccurs="1" name="RejectionReason" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="MIRequired" type="s:boolean"/>
            <s:element minOccurs="0" maxOccurs="1" name="ProcessorTransactionID" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RecoverUsername">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="EmailAddress" type="tns:string-1-320"/>
            <s:element minOccurs="0" maxOccurs="1" name="Reserved" type="tns:string-0-11"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RecoverUsernameResponse">
        <s:complexType/>
      </s:element>
      <s:element name="RegisterShippingProviderAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="registerShippingProviderAccountRequest" type="tns:RegisterShippingProviderAccountRequest"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="RegisterShippingProviderAccountRequest">
        <s:sequence>
          <s:choice minOccurs="1" maxOccurs="1">
            <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
          </s:choice>
          <s:element minOccurs="0" maxOccurs="1" name="Zorbit" type="tns:Zorbit"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Zorbit">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Email" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Password" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Company" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Phone" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="RegisterShippingProviderAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ReprintIndicium">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="indiciumRequest" type="tns:ReprintIndiciumRequest"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ReprintIndiciumRequest">
        <s:sequence>
          <s:choice minOccurs="1" maxOccurs="1">
            <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
          </s:choice>
          <s:element minOccurs="0" maxOccurs="1" name="IntegratorTxId" type="tns:string-0-128"/>
          <s:element minOccurs="0" maxOccurs="1" name="StampsTxId" type="tns:string-0-128"/>
          <s:element minOccurs="0" maxOccurs="1" name="TrackingNumber" type="tns:string-0-128"/>
          <s:element minOccurs="0" maxOccurs="1" default="Auto" name="ImageType" type="tns:ImageType"/>
          <s:element minOccurs="0" maxOccurs="1" default="ImageDpiDefault" name="ImageDpi" type="tns:ImageDpi"/>
          <s:element minOccurs="0" maxOccurs="1" default="Default" name="EltronPrinterDpiType" type="tns:EltronPrinterDPIType"/>
          <s:element minOccurs="1" maxOccurs="1" name="RotationDegrees" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="-1" name="HorizontalOffset" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="-1" name="VerticalOffset" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="-1" name="PrintDensity" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="Default" name="PaperSize" type="tns:PaperSizeV1"/>
          <s:element minOccurs="1" maxOccurs="1" name="StartRow" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="StartColumn" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnImageData" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" default="false" name="ReturnIndiciumData" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:element name="ReprintIndiciumResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ReprintIndiciumResult" type="tns:ReprintIndiciumResponse"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ReprintIndiciumResponse">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="IntegratorTxId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="TrackingNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="FromZipCode" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Amount" nillable="true" type="s:decimal"/>
          <s:element minOccurs="1" maxOccurs="1" name="ServiceType" nillable="true" type="tns:MailClass"/>
          <s:element minOccurs="1" maxOccurs="1" name="ShipDate" nillable="true" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="From" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" name="To" type="tns:Address"/>
          <s:element minOccurs="0" maxOccurs="1" name="StampsTxId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Url" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="FormUrl" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ImageData" type="tns:ArrayOfBase64Binary"/>
          <s:element minOccurs="0" maxOccurs="1" name="IndiciaData" type="tns:ArrayOfIndiciumData"/>
          <s:element minOccurs="1" maxOccurs="1" name="IssuedLabelCount" nillable="true" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="MailClass">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown"/>
          <s:enumeration value="FirstClass"/>
          <s:enumeration value="Priority"/>
          <s:enumeration value="Express"/>
          <s:enumeration value="ParcelPost"/>
          <s:enumeration value="MediaMail"/>
          <s:enumeration value="BoundPrintedMatter"/>
          <s:enumeration value="MailClassIntlGxg"/>
          <s:enumeration value="MailClassIntlAirmailMBag"/>
          <s:enumeration value="MailClassIntlMatterForBlind"/>
          <s:enumeration value="LibraryMail"/>
          <s:enumeration value="ExpressMailInternational"/>
          <s:enumeration value="PriorityMailInternational"/>
          <s:enumeration value="FirstClassMailInternational"/>
          <s:enumeration value="MailClassIntlMBag"/>
          <s:enumeration value="MailClassIntlFirstClassParcel"/>
          <s:enumeration value="MailClassIntlFirstClassFlat"/>
          <s:enumeration value="Critical"/>
          <s:enumeration value="ParcelSelect"/>
          <s:enumeration value="DhlSMParcelsExpedited"/>
          <s:enumeration value="DhlSMParcelsGround"/>
          <s:enumeration value="DhlSMParcelPlusExpedited"/>
          <s:enumeration value="DhlSMParcelPlusGround"/>
          <s:enumeration value="DhlSMBpmExpedited"/>
          <s:enumeration value="DhlSMBpmGround"/>
          <s:enumeration value="DhlSMMarketingParcelExpedited"/>
          <s:enumeration value="DhlSMMarketingParcelGround"/>
          <s:enumeration value="StandardMailMarketingParcels"/>
          <s:enumeration value="AsendiaIpa"/>
          <s:enumeration value="AsendiaIsal"/>
          <s:enumeration value="AsendiaEpkt"/>
          <s:enumeration value="DhlGlobalmailPacketIpa"/>
          <s:enumeration value="DHLGlobalmailPacketIsal"/>
          <s:enumeration value="GlobegisticsIpa"/>
          <s:enumeration value="GlobegisticsIsal"/>
          <s:enumeration value="GlobegisticsEpkt"/>
          <s:enumeration value="InternationalBondedCouriersIpa"/>
          <s:enumeration value="InternationalBondedCouriersIsal"/>
          <s:enumeration value="InternationalBondedCouriersEpkt"/>
          <s:enumeration value="RrdIpa"/>
          <s:enumeration value="RrdIsal"/>
          <s:enumeration value="RrdEpkt"/>
          <s:enumeration value="UspsIpa"/>
          <s:enumeration value="UspsIsal"/>
          <s:enumeration value="UspsEpkt"/>
          <s:enumeration value="AsendiaGeneric"/>
          <s:enumeration value="GlobegisticsGeneric"/>
          <s:enumeration value="ScGeneric"/>
          <s:enumeration value="RrdGeneric"/>
          <s:enumeration value="GlobalPostEconomy"/>
          <s:enumeration value="GlobalPostPriority"/>
          <s:enumeration value="GlobalPostEconomySmartSaver"/>
          <s:enumeration value="GlobalPostPrioritySmartSaver"/>
          <s:enumeration value="DHLExpress"/>
          <s:enumeration value="FedExGround"/>
          <s:enumeration value="FedExHomeDelivery"/>
          <s:enumeration value="FedEx2Day"/>
          <s:enumeration value="FedExExpressSaver"/>
          <s:enumeration value="FedExStandardOvernight"/>
          <s:enumeration value="FedExPriorityOvernight"/>
          <s:enumeration value="FedExInternationalGround"/>
          <s:enumeration value="FedExInternationalEconomy"/>
          <s:enumeration value="FedExInternationalPriority"/>
          <s:enumeration value="GlobalPostPlusSmartSaver"/>
          <s:enumeration value="RetailGround"/>
          <s:enumeration value="GlobalPostPlus"/>
          <s:enumeration value="UPSNextDayAirEarly"/>
          <s:enumeration value="UPSNextDayAir"/>
          <s:enumeration value="UPSNextDayAirSaver"/>
          <s:enumeration value="UPS2ndDayAirAM"/>
          <s:enumeration value="UPS2ndDayAir"/>
          <s:enumeration value="UPS3DaySelect"/>
          <s:enumeration value="UPSGround"/>
          <s:enumeration value="UPSStandard"/>
          <s:enumeration value="UPSWorldwideExpress"/>
          <s:enumeration value="UPSWorldwideExpressPlus"/>
          <s:enumeration value="UPSWorldwideExpedited"/>
          <s:enumeration value="UPSWorldwideSaver"/>
          <s:enumeration value="GlobalPostFCI"/>
          <s:enumeration value="GlobalPostFCISS"/>
          <s:enumeration value="GlobalPostPMI"/>
          <s:enumeration value="GlobalPostPMISS"/>
          <s:enumeration value="GlobalPostEMI"/>
          <s:enumeration value="GlobalPostEMISS"/>
          <s:enumeration value="GlobalPostFCSS"/>
          <s:enumeration value="GlobalPostPSSS"/>
          <s:enumeration value="CanadaPostPriority"/>
          <s:enumeration value="CanadaPostXpressPost"/>
          <s:enumeration value="CanadaPostExpeditedParcel"/>
          <s:enumeration value="CanadaPostRegularParcel"/>
          <s:enumeration value="CanadaPostPriorityWorldWide"/>
          <s:enumeration value="CanadaPostPriorityWorldWideUSA"/>
          <s:enumeration value="CanadaPostXpresspostInternational"/>
          <s:enumeration value="CanadaPostXpresspostUSA"/>
          <s:enumeration value="CanadaPostExpeditedParcelUSA"/>
          <s:enumeration value="CanadaPostTrackedPacketInternational"/>
          <s:enumeration value="CanadaPostTrackedPackageUSA"/>
          <s:enumeration value="CanadaPostSmallPacketInternationalAir"/>
          <s:enumeration value="CanadaPostSmallPacketInternationalSurface"/>
          <s:enumeration value="CanadaPostSmallPacketUSAAir"/>
          <s:enumeration value="CanadaPostInternationalParcelAir"/>
          <s:enumeration value="CanadaPostInternationalParcelSurface"/>
          <s:enumeration value="FedEx2DayAM"/>
          <s:enumeration value="FedExFirstOvernight"/>
          <s:enumeration value="FedExInternationalFirst"/>
          <s:enumeration value="UspsReturn"/>
          <s:enumeration value="DhlGlobalmailPacketIpl"/>
          <s:enumeration value="DhlGlobalmailPacketIpr"/>
          <s:enumeration value="DhlGlobalmailPacketIst"/>
          <s:enumeration value="DhlGlobalmailParcelIdep"/>
          <s:enumeration value="DhlGlobalmailParcelIdeu"/>
          <s:enumeration value="DhlGlobalmailParcelIdpp"/>
          <s:enumeration value="DhlGlobalmailParcelIdpu"/>
          <s:enumeration value="DhlGlobalmailParcelIp"/>
          <s:enumeration value="DhlGlobalmailParcelIs"/>
          <s:enumeration value="DhlPacketInternational"/>
          <s:enumeration value="DhlParcelInternationalDirect"/>
          <s:enumeration value="DhlSMParcelExpeditedMax"/>
          <s:enumeration value="UPSGroundSaver"/>
          <s:enumeration value="GroundAdvantage"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="ResubmitPurchase">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ResubmitCookie" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ResubmitPurchaseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TransactionID" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="ChargedAmount" type="s:decimal"/>
            <s:element minOccurs="1" maxOccurs="1" name="PendingAmount" type="s:decimal"/>
            <s:element minOccurs="1" maxOccurs="1" name="WaitIntervalSeconds" type="s:int"/>
            <s:element minOccurs="1" maxOccurs="1" name="PurchaseStatus" type="tns:PurchaseStatus"/>
            <s:element minOccurs="0" maxOccurs="1" name="RejectionReason" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ScheduleCarrierPickup">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="ContactInformation" type="tns:CarrierPickupContactInformationV2"/>
            <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:CarrierPickupAddressV3"/>
            <s:element minOccurs="1" maxOccurs="1" name="PackageInformation" type="tns:CarrierPickupPackageInformation"/>
            <s:element minOccurs="0" maxOccurs="1" default="USPS" name="Carrier" type="tns:Carrier"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeEarliest" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeLatest" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="Default" name="PickupType" type="tns:CarrierPickupType"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="CarrierPickupAddressV3">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Company" type="tns:string-0-50"/>
          <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:string-1-50"/>
          <s:element minOccurs="1" maxOccurs="1" name="City" type="tns:string-1-35"/>
          <s:element minOccurs="1" maxOccurs="1" name="State" type="tns:string-2-2"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZIP" type="tns:string-5-5"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="ZIP4" type="tns:string-0-4"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Address2" type="tns:string-0-50"/>
          <s:element minOccurs="0" maxOccurs="1" default="" name="Address3" type="tns:string-0-50"/>
        </s:sequence>
      </s:complexType>
      <s:element name="ScheduleCarrierPickupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickupDate" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="PickUpDayOfWeek" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="ConfirmationNumber" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeEarliest" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" default="" name="PickupTimeLatest" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="EstimatedAmount" type="s:decimal"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetAutoBuy">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="1" maxOccurs="1" name="AutoBuySettings" type="tns:AutoBuySettings"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetAutoBuyResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetCodeWords">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword1Type" type="tns:CodewordType"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword1" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword2Type" type="tns:CodewordType"/>
            <s:element minOccurs="0" maxOccurs="1" name="Codeword2" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetCodeWordsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StartAccountVerification">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" name="PhoneNumber" type="tns:string-10-10"/>
            <s:element minOccurs="0" maxOccurs="1" name="Extension" type="tns:string-0-6"/>
            <s:element minOccurs="1" maxOccurs="1" name="PhoneVerificationOption" type="tns:PhoneVerificationOption"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="PhoneVerificationOption">
        <s:restriction base="s:string">
          <s:enumeration value="SMS"/>
          <s:enumeration value="Voice"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="StartAccountVerificationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StartPasswordReset">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Username" type="tns:string-0-40"/>
            <s:element minOccurs="1" maxOccurs="1" name="Codeword1" type="tns:string-0-40"/>
            <s:element minOccurs="1" maxOccurs="1" name="Codeword2" type="tns:string-0-40"/>
            <s:element minOccurs="0" maxOccurs="1" name="IntegrationId" type="s1:guid"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StartPasswordResetResponse">
        <s:complexType/>
      </s:element>
      <s:element name="TrackShipment">
        <s:complexType>
          <s:sequence>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="Authenticator" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Credentials" type="tns:Credentials"/>
            </s:choice>
            <s:choice minOccurs="1" maxOccurs="1">
              <s:element minOccurs="0" maxOccurs="1" name="TrackingNumber" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="StampsTxID" type="s1:guid"/>
            </s:choice>
            <s:element minOccurs="0" maxOccurs="1" default="All" name="Carrier" type="tns:Carrier"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TrackShipmentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Authenticator" type="s:string"/>
            <s:element minOccurs="1" maxOccurs="1" name="TrackingEvents" type="tns:ArrayOfTrackingEvent"/>
            <s:element minOccurs="1" maxOccurs="1" name="GuaranteedDeliveryDate" nillable="true" type="s:dateTime"/>
            <s:element minOccurs="1" maxOccurs="1" name="ExpectedDeliveryDate" nillable="true" type="s:dateTime"/>
            <s:element minOccurs="0" maxOccurs="1" name="ServiceDescription" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="Carrier" type="s:string"/>
            <s:element minOccurs="0" maxOccurs="1" name="DestinationInfo" type="tns:DestinationInfo"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfTrackingEvent">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="TrackingEvent" nillable="true" type="tns:TrackingEvent"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TrackingEvent">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Timestamp" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Event" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="TrackingEventType" type="tns:TrackingEventType"/>
          <s:element minOccurs="1" maxOccurs="1" name="City" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="State" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Zip" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Country" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SignedBy" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AuthorizedAgent" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="TrackingEventType">
        <s:restriction base="s:string">
          <s:enumeration value="ManifestAcknowledgement"/>
          <s:enumeration value="ElectronicNotification"/>
          <s:enumeration value="Delivered"/>
          <s:enumeration value="AttemptedNoticeLeft"/>
          <s:enumeration value="AcceptOrPickup"/>
          <s:enumeration value="Refused"/>
          <s:enumeration value="Undeliverable"/>
          <s:enumeration value="Forwarded"/>
          <s:enumeration value="ArrivalAtUnit"/>
          <s:enumeration value="Missent"/>
          <s:enumeration value="ReturnToSender"/>
          <s:enumeration value="Enroute"/>
          <s:enumeration value="DeadLetter"/>
          <s:enumeration value="ArrivalAtPickupPoint"/>
          <s:enumeration value="NoSuchNumber"/>
          <s:enumeration value="InsufficientAddress"/>
          <s:enumeration value="MovedNoForwarding"/>
          <s:enumeration value="ForwardingExpired"/>
          <s:enumeration value="AddresseeUnknown"/>
          <s:enumeration value="Vacant"/>
          <s:enumeration value="Unclaimed"/>
          <s:enumeration value="Deceased"/>
          <s:enumeration value="ReturnedOtherReason"/>
          <s:enumeration value="PickedUpByShippingPartner"/>
          <s:enumeration value="ArrivedAtShippingPartner"/>
          <s:enumeration value="DepartedFromShippingPartner"/>
          <s:enumeration value="USPS Event"/>
          <s:enumeration value="ArriveSortFacility_A1"/>
          <s:enumeration value="AcceptedAtDestination"/>
          <s:enumeration value="ArrivedUSPSSortFacility"/>
          <s:enumeration value="Processed_BE"/>
          <s:enumeration value="DepartUSPSSortFacility"/>
          <s:enumeration value="DeliveryStatusNotUpdated"/>
          <s:enumeration value="DispatchedFromSortFacility"/>
          <s:enumeration value="ShippingLabelCreated"/>
          <s:enumeration value="DepartSortFacility_L1"/>
          <s:enumeration value="PickedUpAndProcessedByAgent"/>
          <s:enumeration value="OriginAcceptance"/>
          <s:enumeration value="ProcessedAtDestinationFacility"/>
          <s:enumeration value="OutForDelivery"/>
          <s:enumeration value="Sorting_ProcessingComplete"/>
          <s:enumeration value="DispatchedToSortFacility"/>
          <s:enumeration value="ArriveSortFacility_R1"/>
          <s:enumeration value="Processed_RB"/>
          <s:enumeration value="DepartSortFacility_T1"/>
          <s:enumeration value="ShipmentAcceptance"/>
          <s:enumeration value="In_processAcceptance"/>
          <s:enumeration value="ArriveSortFacility_U1"/>
          <s:enumeration value="Mis_shipped"/>
          <s:enumeration value="AvailableForPickup"/>
          <s:enumeration value="PickedUpByAgent"/>
          <s:enumeration value="ReturnToSender_NotPickedUp"/>
          <s:enumeration value="DeadMail_DisposedByPostOffice"/>
          <s:enumeration value="DeadMail_SentToRecoveryCenter"/>
          <s:enumeration value="Processed_RegisteredMailOnly_35"/>
          <s:enumeration value="Processed_RegisteredMailOnly_36"/>
          <s:enumeration value="Processed_RegisteredMailOnly_38"/>
          <s:enumeration value="Processed_RegisteredMailOnly_39"/>
          <s:enumeration value="Processed_RegisteredMailOnly_40"/>
          <s:enumeration value="ReceivedAtOpeningUnit"/>
          <s:enumeration value="USPSHandoffToShippingPartner"/>
          <s:enumeration value="PickedUp"/>
          <s:enumeration value="CustomerRecall"/>
          <s:enumeration value="DispatchedToMilitary"/>
          <s:enumeration value="DuplicateLabelID"/>
          <s:enumeration value="BusinessClosed"/>
          <s:enumeration value="NoticeLeft"/>
          <s:enumeration value="ReceptacleBlocked"/>
          <s:enumeration value="ReceptacleFull"/>
          <s:enumeration value="NoSecureLocationAvailable"/>
          <s:enumeration value="NoAuthorizedRecipientAvailable"/>
          <s:enumeration value="TenderedToAgentForFinalDelivery"/>
          <s:enumeration value="TenderedToPostalService"/>
          <s:enumeration value="ArrivedAgentFacility"/>
          <s:enumeration value="DepartedAgentFacility"/>
          <s:enumeration value="DeliveredByAgentToMerchant"/>
          <s:enumeration value="FinalDispositionByMerchant"/>
          <s:enumeration value="MailConsigned"/>
          <s:enumeration value="ForeginReceipt"/>
          <s:enumeration value="CustomsClearance"/>
          <s:enumeration value="RecipientActionRequired"/>
          <s:enumeration value="Delay"/>
          <s:enumeration value="ReturnToSender_CO"/>
          <s:enumeration value="AttemptedNoticeLeft_H0"/>
          <s:enumeration value="ServiceDelay"/>
          <s:enumeration value="ErrorDataNotAvailable"/>
          <s:enumeration value="None"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DestinationInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DestinationCity" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DestinationState" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DestinationZip" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DestinationCountry" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="string-0-50">
        <s:restriction base="s:string">
          <s:maxLength value="50"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-30">
        <s:restriction base="s:string">
          <s:maxLength value="30"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-5">
        <s:restriction base="s:string">
          <s:maxLength value="5"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-4">
        <s:restriction base="s:string">
          <s:maxLength value="4"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-2-2">
        <s:restriction base="s:string">
          <s:minLength value="2"/>
          <s:maxLength value="2"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-28">
        <s:restriction base="s:string">
          <s:maxLength value="28"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="double-le-999">
        <s:restriction base="s:double">
          <s:maxInclusive value="999"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-128">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="128"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-128">
        <s:restriction base="s:string">
          <s:maxLength value="128"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-64">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="64"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-76">
        <s:restriction base="s:string">
          <s:maxLength value="76"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-16">
        <s:restriction base="s:string">
          <s:maxLength value="16"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-12">
        <s:restriction base="s:string">
          <s:maxLength value="12"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-15">
        <s:restriction base="s:string">
          <s:maxLength value="15"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-25">
        <s:restriction base="s:string">
          <s:maxLength value="25"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-2-60">
        <s:restriction base="s:string">
          <s:minLength value="2"/>
          <s:maxLength value="60"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="double-ge-1">
        <s:restriction base="s:double">
          <s:minInclusive value="1"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="decimal-ge-0">
        <s:restriction base="s:decimal">
          <s:minInclusive value="0"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-20">
        <s:restriction base="s:string">
          <s:maxLength value="20"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-2-100">
        <s:restriction base="s:string">
          <s:minLength value="2"/>
          <s:maxLength value="100"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-14">
        <s:restriction base="s:string">
          <s:maxLength value="14"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-35">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="35"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-100">
        <s:restriction base="s:string">
          <s:maxLength value="100"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-8192">
        <s:restriction base="s:string">
          <s:maxLength value="8192"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-3">
        <s:restriction base="s:string">
          <s:maxLength value="3"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-10">
        <s:restriction base="s:string">
          <s:maxLength value="10"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-150">
        <s:restriction base="s:string">
          <s:maxLength value="150"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-40">
        <s:restriction base="s:string">
          <s:maxLength value="40"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-6-20">
        <s:restriction base="s:string">
          <s:minLength value="6"/>
          <s:maxLength value="20"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-320">
        <s:restriction base="s:string">
          <s:maxLength value="320"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-4000">
        <s:restriction base="s:string">
          <s:maxLength value="4000"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-50">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="50"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-30">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="30"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-10">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="10"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-35">
        <s:restriction base="s:string">
          <s:maxLength value="35"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-5-5">
        <s:restriction base="s:string">
          <s:minLength value="5"/>
          <s:maxLength value="5"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-2-2147483647">
        <s:restriction base="s:string">
          <s:minLength value="2"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-2147483647">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-40">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="40"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-29">
        <s:restriction base="s:string">
          <s:maxLength value="29"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-6">
        <s:restriction base="s:string">
          <s:maxLength value="6"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-10-10">
        <s:restriction base="s:string">
          <s:minLength value="10"/>
          <s:maxLength value="10"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-75">
        <s:restriction base="s:string">
          <s:maxLength value="75"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="integer-ge-1-le-500">
        <s:restriction base="s:integer">
          <s:minInclusive value="1"/>
          <s:maxInclusive value="500"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-100">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="100"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-1-320">
        <s:restriction base="s:string">
          <s:minLength value="1"/>
          <s:maxLength value="320"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="string-0-11">
        <s:restriction base="s:string">
          <s:maxLength value="11"/>
        </s:restriction>
      </s:simpleType>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://microsoft.com/wsdl/types/">
      <s:simpleType name="guid">
        <s:restriction base="s:string">
          <s:pattern value="[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"/>
        </s:restriction>
      </s:simpleType>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://stamps.com/xml/namespace/2014/3/addressservicev2">
      <s:complexType name="HoldForPickUpFacility">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Id" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address" type="s2:Address"/>
          <s:element minOccurs="1" maxOccurs="1" name="Has10AMCommitment" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Address">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="FullName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="NamePrefix" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="FirstName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="MiddleName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="LastName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="NameSuffix" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Title" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Department" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Company" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address1" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address2" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Address3" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="City" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="State" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ZIPCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ZIPCodeAddOn" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="DPB" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CheckDigit" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Province" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PostalCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" default="US" name="Country" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Urbanization" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PhoneNumber" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="CleanseHash" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="OverrideHash" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AMSAddress">
        <s:complexContent mixed="false">
          <s:extension base="s2:Address">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="HasPrefixRange" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" default="All" name="PrefixRangeType" type="s2:RangeType"/>
              <s:element minOccurs="0" maxOccurs="1" name="PrefixRangeStart" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="PrefixRangeEnd" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="HasSuffixRange" type="s:boolean"/>
              <s:element minOccurs="0" maxOccurs="1" default="All" name="SuffixRangeType" type="s2:RangeType"/>
              <s:element minOccurs="0" maxOccurs="1" name="SuffixRangeStart" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="SuffixRangeEnd" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Rank" type="s:int"/>
              <s:element minOccurs="1" maxOccurs="1" name="WasDefault" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="Displayable" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="ExpirationDate" type="s:dateTime"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:simpleType name="RangeType">
        <s:restriction base="s:string">
          <s:enumeration value="All"/>
          <s:enumeration value="Even"/>
          <s:enumeration value="Odd"/>
        </s:restriction>
      </s:simpleType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="GetRatesSoapIn">
    <wsdl:part name="parameters" element="tns:GetRates"/>
  </wsdl:message>
  <wsdl:message name="GetRatesSoapOut">
    <wsdl:part name="parameters" element="tns:GetRatesResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateIndiciumSoapIn">
    <wsdl:part name="parameters" element="tns:CreateIndicium"/>
  </wsdl:message>
  <wsdl:message name="CreateIndiciumSoapOut">
    <wsdl:part name="parameters" element="tns:CreateIndiciumResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateEnvelopeIndiciumSoapIn">
    <wsdl:part name="parameters" element="tns:CreateEnvelopeIndicium"/>
  </wsdl:message>
  <wsdl:message name="CreateEnvelopeIndiciumSoapOut">
    <wsdl:part name="parameters" element="tns:CreateEnvelopeIndiciumResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateMailingLabelIndiciaSoapIn">
    <wsdl:part name="parameters" element="tns:CreateMailingLabelIndicia"/>
  </wsdl:message>
  <wsdl:message name="CreateMailingLabelIndiciaSoapOut">
    <wsdl:part name="parameters" element="tns:CreateMailingLabelIndiciaResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateNetStampsIndiciaSoapIn">
    <wsdl:part name="parameters" element="tns:CreateNetStampsIndicia"/>
  </wsdl:message>
  <wsdl:message name="CreateNetStampsIndiciaSoapOut">
    <wsdl:part name="parameters" element="tns:CreateNetStampsIndiciaResponse"/>
  </wsdl:message>
  <wsdl:message name="RegisterAccountSoapIn">
    <wsdl:part name="parameters" element="tns:RegisterAccount"/>
  </wsdl:message>
  <wsdl:message name="RegisterAccountSoapOut">
    <wsdl:part name="parameters" element="tns:RegisterAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="AddCarrierSoapIn">
    <wsdl:part name="parameters" element="tns:AddCarrier"/>
  </wsdl:message>
  <wsdl:message name="AddCarrierSoapOut">
    <wsdl:part name="parameters" element="tns:AddCarrierResponse"/>
  </wsdl:message>
  <wsdl:message name="AddImageSoapIn">
    <wsdl:part name="parameters" element="tns:AddImage"/>
  </wsdl:message>
  <wsdl:message name="AddImageSoapOut">
    <wsdl:part name="parameters" element="tns:AddImageResponse"/>
  </wsdl:message>
  <wsdl:message name="AddUserPaymentMethodSoapIn">
    <wsdl:part name="parameters" element="tns:AddUserPaymentMethod"/>
  </wsdl:message>
  <wsdl:message name="AddUserPaymentMethodSoapOut">
    <wsdl:part name="parameters" element="tns:AddUserPaymentMethodResponse"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateBridgeAuthenticatorSoapIn">
    <wsdl:part name="parameters" element="tns:AuthenticateBridgeAuthenticator"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateBridgeAuthenticatorSoapOut">
    <wsdl:part name="parameters" element="tns:AuthenticateBridgeAuthenticatorResponse"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateUserSoapIn">
    <wsdl:part name="parameters" element="tns:AuthenticateUser"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateUserSoapOut">
    <wsdl:part name="parameters" element="tns:AuthenticateUserResponse"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateWithTransferAuthenticatorSoapIn">
    <wsdl:part name="parameters" element="tns:AuthenticateWithTransferAuthenticator"/>
  </wsdl:message>
  <wsdl:message name="AuthenticateWithTransferAuthenticatorSoapOut">
    <wsdl:part name="parameters" element="tns:AuthenticateWithTransferAuthenticatorResponse"/>
  </wsdl:message>
  <wsdl:message name="CancelAccountSoapIn">
    <wsdl:part name="parameters" element="tns:CancelAccount"/>
  </wsdl:message>
  <wsdl:message name="CancelAccountSoapOut">
    <wsdl:part name="parameters" element="tns:CancelAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="CancelCarrierPickupSoapIn">
    <wsdl:part name="parameters" element="tns:CancelCarrierPickup"/>
  </wsdl:message>
  <wsdl:message name="CancelCarrierPickupSoapOut">
    <wsdl:part name="parameters" element="tns:CancelCarrierPickupResponse"/>
  </wsdl:message>
  <wsdl:message name="CancelIndiciumSoapIn">
    <wsdl:part name="parameters" element="tns:CancelIndicium"/>
  </wsdl:message>
  <wsdl:message name="CancelIndiciumSoapOut">
    <wsdl:part name="parameters" element="tns:CancelIndiciumResponse"/>
  </wsdl:message>
  <wsdl:message name="ChangeDefaultPaymentMethodSoapIn">
    <wsdl:part name="parameters" element="tns:ChangeDefaultPaymentMethod"/>
  </wsdl:message>
  <wsdl:message name="ChangeDefaultPaymentMethodSoapOut">
    <wsdl:part name="parameters" element="tns:ChangeDefaultPaymentMethodResponse"/>
  </wsdl:message>
  <wsdl:message name="ChangePasswordSoapIn">
    <wsdl:part name="parameters" element="tns:ChangePassword"/>
  </wsdl:message>
  <wsdl:message name="ChangePasswordSoapOut">
    <wsdl:part name="parameters" element="tns:ChangePasswordResponse"/>
  </wsdl:message>
  <wsdl:message name="ChangePlanSoapIn">
    <wsdl:part name="parameters" element="tns:ChangePlan"/>
  </wsdl:message>
  <wsdl:message name="ChangePlanSoapOut">
    <wsdl:part name="parameters" element="tns:ChangePlanResponse"/>
  </wsdl:message>
  <wsdl:message name="CheckCarrierPickupAvailabilitySoapIn">
    <wsdl:part name="parameters" element="tns:CheckCarrierPickupAvailability"/>
  </wsdl:message>
  <wsdl:message name="CheckCarrierPickupAvailabilitySoapOut">
    <wsdl:part name="parameters" element="tns:CheckCarrierPickupAvailabilityResponse"/>
  </wsdl:message>
  <wsdl:message name="CleanseAddressSoapIn">
    <wsdl:part name="parameters" element="tns:CleanseAddress"/>
  </wsdl:message>
  <wsdl:message name="CleanseAddressSoapOut">
    <wsdl:part name="parameters" element="tns:CleanseAddressResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateBrandingSoapIn">
    <wsdl:part name="parameters" element="tns:CreateBranding"/>
  </wsdl:message>
  <wsdl:message name="CreateBrandingSoapOut">
    <wsdl:part name="parameters" element="tns:CreateBrandingResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateManifestSoapIn">
    <wsdl:part name="parameters" element="tns:CreateManifest"/>
  </wsdl:message>
  <wsdl:message name="CreateManifestSoapOut">
    <wsdl:part name="parameters" element="tns:CreateManifestResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateNotificationSettingSoapIn">
    <wsdl:part name="parameters" element="tns:CreateNotificationSetting"/>
  </wsdl:message>
  <wsdl:message name="CreateNotificationSettingSoapOut">
    <wsdl:part name="parameters" element="tns:CreateNotificationSettingResponse"/>
  </wsdl:message>
  <wsdl:message name="CreateShipmentNotificationSoapIn">
    <wsdl:part name="parameters" element="tns:CreateShipmentNotification"/>
  </wsdl:message>
  <wsdl:message name="CreateShipmentNotificationSoapOut">
    <wsdl:part name="parameters" element="tns:CreateShipmentNotificationResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteBrandingSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteBranding"/>
  </wsdl:message>
  <wsdl:message name="DeleteBrandingSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteBrandingResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteCarrierSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCarrier"/>
  </wsdl:message>
  <wsdl:message name="DeleteCarrierSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCarrierResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteImageSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteImage"/>
  </wsdl:message>
  <wsdl:message name="DeleteImageSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteImageResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteNotificationSettingSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteNotificationSetting"/>
  </wsdl:message>
  <wsdl:message name="DeleteNotificationSettingSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteNotificationSettingResponse"/>
  </wsdl:message>
  <wsdl:message name="DeleteUserPaymentMethodSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteUserPaymentMethod"/>
  </wsdl:message>
  <wsdl:message name="DeleteUserPaymentMethodSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteUserPaymentMethodResponse"/>
  </wsdl:message>
  <wsdl:message name="EnumCodeWordTypesSoapIn">
    <wsdl:part name="parameters" element="tns:EnumCodeWordTypes"/>
  </wsdl:message>
  <wsdl:message name="EnumCodeWordTypesSoapOut">
    <wsdl:part name="parameters" element="tns:EnumCodeWordTypesResponse"/>
  </wsdl:message>
  <wsdl:message name="EnumCostCodesSoapIn">
    <wsdl:part name="parameters" element="tns:EnumCostCodes"/>
  </wsdl:message>
  <wsdl:message name="EnumCostCodesSoapOut">
    <wsdl:part name="parameters" element="tns:EnumCostCodesResponse"/>
  </wsdl:message>
  <wsdl:message name="EnumNetStampsLayoutsSoapIn">
    <wsdl:part name="parameters" element="tns:EnumNetStampsLayouts"/>
  </wsdl:message>
  <wsdl:message name="EnumNetStampsLayoutsSoapOut">
    <wsdl:part name="parameters" element="tns:EnumNetStampsLayoutsResponse"/>
  </wsdl:message>
  <wsdl:message name="FinishAccountVerificationSoapIn">
    <wsdl:part name="parameters" element="tns:FinishAccountVerification"/>
  </wsdl:message>
  <wsdl:message name="FinishAccountVerificationSoapOut">
    <wsdl:part name="parameters" element="tns:FinishAccountVerificationResponse"/>
  </wsdl:message>
  <wsdl:message name="FinishPasswordResetSoapIn">
    <wsdl:part name="parameters" element="tns:FinishPasswordReset"/>
  </wsdl:message>
  <wsdl:message name="FinishPasswordResetSoapOut">
    <wsdl:part name="parameters" element="tns:FinishPasswordResetResponse"/>
  </wsdl:message>
  <wsdl:message name="GetAccountInfoSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccountInfo"/>
  </wsdl:message>
  <wsdl:message name="GetAccountInfoSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccountInfoResponse"/>
  </wsdl:message>
  <wsdl:message name="GetBalanceHistorySoapIn">
    <wsdl:part name="parameters" element="tns:GetBalanceHistory"/>
  </wsdl:message>
  <wsdl:message name="GetBalanceHistorySoapOut">
    <wsdl:part name="parameters" element="tns:GetBalanceHistoryResponse"/>
  </wsdl:message>
  <wsdl:message name="GetBalanceHistoryByTokenSoapIn">
    <wsdl:part name="parameters" element="tns:GetBalanceHistoryByToken"/>
  </wsdl:message>
  <wsdl:message name="GetBalanceHistoryByTokenSoapOut">
    <wsdl:part name="parameters" element="tns:GetBalanceHistoryByTokenResponse"/>
  </wsdl:message>
  <wsdl:message name="GetBrandingSoapIn">
    <wsdl:part name="parameters" element="tns:GetBranding"/>
  </wsdl:message>
  <wsdl:message name="GetBrandingSoapOut">
    <wsdl:part name="parameters" element="tns:GetBrandingResponse"/>
  </wsdl:message>
  <wsdl:message name="GetCarrierPickupListSoapIn">
    <wsdl:part name="parameters" element="tns:GetCarrierPickupList"/>
  </wsdl:message>
  <wsdl:message name="GetCarrierPickupListSoapOut">
    <wsdl:part name="parameters" element="tns:GetCarrierPickupListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetChangePlanStatusSoapIn">
    <wsdl:part name="parameters" element="tns:GetChangePlanStatus"/>
  </wsdl:message>
  <wsdl:message name="GetChangePlanStatusSoapOut">
    <wsdl:part name="parameters" element="tns:GetChangePlanStatusResponse"/>
  </wsdl:message>
  <wsdl:message name="GetCodewordQuestionsSoapIn">
    <wsdl:part name="parameters" element="tns:GetCodewordQuestions"/>
  </wsdl:message>
  <wsdl:message name="GetCodewordQuestionsSoapOut">
    <wsdl:part name="parameters" element="tns:GetCodewordQuestionsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetImageListSoapIn">
    <wsdl:part name="parameters" element="tns:GetImageList"/>
  </wsdl:message>
  <wsdl:message name="GetImageListSoapOut">
    <wsdl:part name="parameters" element="tns:GetImageListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetNetStampsImagesSoapIn">
    <wsdl:part name="parameters" element="tns:GetNetStampsImages"/>
  </wsdl:message>
  <wsdl:message name="GetNetStampsImagesSoapOut">
    <wsdl:part name="parameters" element="tns:GetNetStampsImagesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetNotificationSettingsSoapIn">
    <wsdl:part name="parameters" element="tns:GetNotificationSettings"/>
  </wsdl:message>
  <wsdl:message name="GetNotificationSettingsSoapOut">
    <wsdl:part name="parameters" element="tns:GetNotificationSettingsResponse"/>
  </wsdl:message>
  <wsdl:message name="GetShipmentListSoapIn">
    <wsdl:part name="parameters" element="tns:GetShipmentList"/>
  </wsdl:message>
  <wsdl:message name="GetShipmentListSoapOut">
    <wsdl:part name="parameters" element="tns:GetShipmentListResponse"/>
  </wsdl:message>
  <wsdl:message name="GetShipmentListByTokenSoapIn">
    <wsdl:part name="parameters" element="tns:GetShipmentListByToken"/>
  </wsdl:message>
  <wsdl:message name="GetShipmentListByTokenSoapOut">
    <wsdl:part name="parameters" element="tns:GetShipmentListByTokenResponse"/>
  </wsdl:message>
  <wsdl:message name="GetSupportedCountriesSoapIn">
    <wsdl:part name="parameters" element="tns:GetSupportedCountries"/>
  </wsdl:message>
  <wsdl:message name="GetSupportedCountriesSoapOut">
    <wsdl:part name="parameters" element="tns:GetSupportedCountriesResponse"/>
  </wsdl:message>
  <wsdl:message name="GetURLSoapIn">
    <wsdl:part name="parameters" element="tns:GetURL"/>
  </wsdl:message>
  <wsdl:message name="GetURLSoapOut">
    <wsdl:part name="parameters" element="tns:GetURLResponse"/>
  </wsdl:message>
  <wsdl:message name="ListPaymentMethodsSoapIn">
    <wsdl:part name="parameters" element="tns:ListPaymentMethods"/>
  </wsdl:message>
  <wsdl:message name="ListPaymentMethodsSoapOut">
    <wsdl:part name="parameters" element="tns:ListPaymentMethodsResponse"/>
  </wsdl:message>
  <wsdl:message name="ModifyBrandingSoapIn">
    <wsdl:part name="parameters" element="tns:ModifyBranding"/>
  </wsdl:message>
  <wsdl:message name="ModifyBrandingSoapOut">
    <wsdl:part name="parameters" element="tns:ModifyBrandingResponse"/>
  </wsdl:message>
  <wsdl:message name="ModifyCarrierPickupSoapIn">
    <wsdl:part name="parameters" element="tns:ModifyCarrierPickup"/>
  </wsdl:message>
  <wsdl:message name="ModifyCarrierPickupSoapOut">
    <wsdl:part name="parameters" element="tns:ModifyCarrierPickupResponse"/>
  </wsdl:message>
  <wsdl:message name="ModifyNotificationSettingSoapIn">
    <wsdl:part name="parameters" element="tns:ModifyNotificationSetting"/>
  </wsdl:message>
  <wsdl:message name="ModifyNotificationSettingSoapOut">
    <wsdl:part name="parameters" element="tns:ModifyNotificationSettingResponse"/>
  </wsdl:message>
  <wsdl:message name="PlaceOrderSoapIn">
    <wsdl:part name="parameters" element="tns:PlaceOrder"/>
  </wsdl:message>
  <wsdl:message name="PlaceOrderSoapOut">
    <wsdl:part name="parameters" element="tns:PlaceOrderResponse"/>
  </wsdl:message>
  <wsdl:message name="PriceOrderSoapIn">
    <wsdl:part name="parameters" element="tns:PriceOrder"/>
  </wsdl:message>
  <wsdl:message name="PriceOrderSoapOut">
    <wsdl:part name="parameters" element="tns:PriceOrderResponse"/>
  </wsdl:message>
  <wsdl:message name="PurchasePostageSoapIn">
    <wsdl:part name="parameters" element="tns:PurchasePostage"/>
  </wsdl:message>
  <wsdl:message name="PurchasePostageSoapOut">
    <wsdl:part name="parameters" element="tns:PurchasePostageResponse"/>
  </wsdl:message>
  <wsdl:message name="RecoverUsernameSoapIn">
    <wsdl:part name="parameters" element="tns:RecoverUsername"/>
  </wsdl:message>
  <wsdl:message name="RecoverUsernameSoapOut">
    <wsdl:part name="parameters" element="tns:RecoverUsernameResponse"/>
  </wsdl:message>
  <wsdl:message name="RegisterShippingProviderAccountSoapIn">
    <wsdl:part name="parameters" element="tns:RegisterShippingProviderAccount"/>
  </wsdl:message>
  <wsdl:message name="RegisterShippingProviderAccountSoapOut">
    <wsdl:part name="parameters" element="tns:RegisterShippingProviderAccountResponse"/>
  </wsdl:message>
  <wsdl:message name="ReprintIndiciumSoapIn">
    <wsdl:part name="parameters" element="tns:ReprintIndicium"/>
  </wsdl:message>
  <wsdl:message name="ReprintIndiciumSoapOut">
    <wsdl:part name="parameters" element="tns:ReprintIndiciumResponse"/>
  </wsdl:message>
  <wsdl:message name="ResubmitPurchaseSoapIn">
    <wsdl:part name="parameters" element="tns:ResubmitPurchase"/>
  </wsdl:message>
  <wsdl:message name="ResubmitPurchaseSoapOut">
    <wsdl:part name="parameters" element="tns:ResubmitPurchaseResponse"/>
  </wsdl:message>
  <wsdl:message name="ScheduleCarrierPickupSoapIn">
    <wsdl:part name="parameters" element="tns:ScheduleCarrierPickup"/>
  </wsdl:message>
  <wsdl:message name="ScheduleCarrierPickupSoapOut">
    <wsdl:part name="parameters" element="tns:ScheduleCarrierPickupResponse"/>
  </wsdl:message>
  <wsdl:message name="SetAutoBuySoapIn">
    <wsdl:part name="parameters" element="tns:SetAutoBuy"/>
  </wsdl:message>
  <wsdl:message name="SetAutoBuySoapOut">
    <wsdl:part name="parameters" element="tns:SetAutoBuyResponse"/>
  </wsdl:message>
  <wsdl:message name="SetCodeWordsSoapIn">
    <wsdl:part name="parameters" element="tns:SetCodeWords"/>
  </wsdl:message>
  <wsdl:message name="SetCodeWordsSoapOut">
    <wsdl:part name="parameters" element="tns:SetCodeWordsResponse"/>
  </wsdl:message>
  <wsdl:message name="StartAccountVerificationSoapIn">
    <wsdl:part name="parameters" element="tns:StartAccountVerification"/>
  </wsdl:message>
  <wsdl:message name="StartAccountVerificationSoapOut">
    <wsdl:part name="parameters" element="tns:StartAccountVerificationResponse"/>
  </wsdl:message>
  <wsdl:message name="StartPasswordResetSoapIn">
    <wsdl:part name="parameters" element="tns:StartPasswordReset"/>
  </wsdl:message>
  <wsdl:message name="StartPasswordResetSoapOut">
    <wsdl:part name="parameters" element="tns:StartPasswordResetResponse"/>
  </wsdl:message>
  <wsdl:message name="TrackShipmentSoapIn">
    <wsdl:part name="parameters" element="tns:TrackShipment"/>
  </wsdl:message>
  <wsdl:message name="TrackShipmentSoapOut">
    <wsdl:part name="parameters" element="tns:TrackShipmentResponse"/>
  </wsdl:message>
  <wsdl:portType name="SwsimV135Soap">
    <wsdl:operation name="GetRates">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Calculate a rate or a list of rates.</wsdl:documentation>
      <wsdl:input message="tns:GetRatesSoapIn"/>
      <wsdl:output message="tns:GetRatesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateIndicium">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Generate an indicium.</wsdl:documentation>
      <wsdl:input message="tns:CreateIndiciumSoapIn"/>
      <wsdl:output message="tns:CreateIndiciumSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateEnvelopeIndicium">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Generate an envelope indicium.</wsdl:documentation>
      <wsdl:input message="tns:CreateEnvelopeIndiciumSoapIn"/>
      <wsdl:output message="tns:CreateEnvelopeIndiciumSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateMailingLabelIndicia">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Generate a mailing label sheet.</wsdl:documentation>
      <wsdl:input message="tns:CreateMailingLabelIndiciaSoapIn"/>
      <wsdl:output message="tns:CreateMailingLabelIndiciaSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateNetStampsIndicia">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Generate NetStamps indicia.</wsdl:documentation>
      <wsdl:input message="tns:CreateNetStampsIndiciaSoapIn"/>
      <wsdl:output message="tns:CreateNetStampsIndiciaSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="RegisterAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Register a new Stamps.com account.</wsdl:documentation>
      <wsdl:input message="tns:RegisterAccountSoapIn"/>
      <wsdl:output message="tns:RegisterAccountSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddCarrier">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add carrier.</wsdl:documentation>
      <wsdl:input message="tns:AddCarrierSoapIn"/>
      <wsdl:output message="tns:AddCarrierSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddImage">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add the image uploaded by the user.</wsdl:documentation>
      <wsdl:input message="tns:AddImageSoapIn"/>
      <wsdl:output message="tns:AddImageSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AddUserPaymentMethod">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add a payment method for the user.</wsdl:documentation>
      <wsdl:input message="tns:AddUserPaymentMethodSoapIn"/>
      <wsdl:output message="tns:AddUserPaymentMethodSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateBridgeAuthenticator">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Authenticate Bridge Authenticator</wsdl:documentation>
      <wsdl:input message="tns:AuthenticateBridgeAuthenticatorSoapIn"/>
      <wsdl:output message="tns:AuthenticateBridgeAuthenticatorSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateUser">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Initial authentication.</wsdl:documentation>
      <wsdl:input message="tns:AuthenticateUserSoapIn"/>
      <wsdl:output message="tns:AuthenticateUserSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateWithTransferAuthenticator">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Authenticate with transfer authenticator.</wsdl:documentation>
      <wsdl:input message="tns:AuthenticateWithTransferAuthenticatorSoapIn"/>
      <wsdl:output message="tns:AuthenticateWithTransferAuthenticatorSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CancelAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Cancel an Account.</wsdl:documentation>
      <wsdl:input message="tns:CancelAccountSoapIn"/>
      <wsdl:output message="tns:CancelAccountSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CancelCarrierPickup">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Cancel existing carrier pickup request.</wsdl:documentation>
      <wsdl:input message="tns:CancelCarrierPickupSoapIn"/>
      <wsdl:output message="tns:CancelCarrierPickupSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CancelIndicium">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Cancel a previously issued indicium.</wsdl:documentation>
      <wsdl:input message="tns:CancelIndiciumSoapIn"/>
      <wsdl:output message="tns:CancelIndiciumSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ChangeDefaultPaymentMethod">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add a payment method for the user.</wsdl:documentation>
      <wsdl:input message="tns:ChangeDefaultPaymentMethodSoapIn"/>
      <wsdl:output message="tns:ChangeDefaultPaymentMethodSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ChangePassword">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Change Password.</wsdl:documentation>
      <wsdl:input message="tns:ChangePasswordSoapIn"/>
      <wsdl:output message="tns:ChangePasswordSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ChangePlan">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Change Plan.</wsdl:documentation>
      <wsdl:input message="tns:ChangePlanSoapIn"/>
      <wsdl:output message="tns:ChangePlanSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CheckCarrierPickupAvailability">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Check carrier pickup availability for an address.</wsdl:documentation>
      <wsdl:input message="tns:CheckCarrierPickupAvailabilitySoapIn"/>
      <wsdl:output message="tns:CheckCarrierPickupAvailabilitySoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CleanseAddress">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Cleanse an address.</wsdl:documentation>
      <wsdl:input message="tns:CleanseAddressSoapIn"/>
      <wsdl:output message="tns:CleanseAddressSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateBranding">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Create Branding.</wsdl:documentation>
      <wsdl:input message="tns:CreateBrandingSoapIn"/>
      <wsdl:output message="tns:CreateBrandingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateManifest">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Generate a Carrier Shipment Manifest.</wsdl:documentation>
      <wsdl:input message="tns:CreateManifestSoapIn"/>
      <wsdl:output message="tns:CreateManifestSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateNotificationSetting">
      <wsdl:input message="tns:CreateNotificationSettingSoapIn"/>
      <wsdl:output message="tns:CreateNotificationSettingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentNotification">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Create Shipment Notification</wsdl:documentation>
      <wsdl:input message="tns:CreateShipmentNotificationSoapIn"/>
      <wsdl:output message="tns:CreateShipmentNotificationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteBranding">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Delete Branding.</wsdl:documentation>
      <wsdl:input message="tns:DeleteBrandingSoapIn"/>
      <wsdl:output message="tns:DeleteBrandingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteCarrier">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Delete carrier.</wsdl:documentation>
      <wsdl:input message="tns:DeleteCarrierSoapIn"/>
      <wsdl:output message="tns:DeleteCarrierSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteImage">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Delete an image uploaded by the user.</wsdl:documentation>
      <wsdl:input message="tns:DeleteImageSoapIn"/>
      <wsdl:output message="tns:DeleteImageSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteNotificationSetting">
      <wsdl:input message="tns:DeleteNotificationSettingSoapIn"/>
      <wsdl:output message="tns:DeleteNotificationSettingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserPaymentMethod">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add a payment method for the user.</wsdl:documentation>
      <wsdl:input message="tns:DeleteUserPaymentMethodSoapIn"/>
      <wsdl:output message="tns:DeleteUserPaymentMethodSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="EnumCodeWordTypes">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Return the list of available CodeWord types.</wsdl:documentation>
      <wsdl:input message="tns:EnumCodeWordTypesSoapIn"/>
      <wsdl:output message="tns:EnumCodeWordTypesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="EnumCostCodes">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of cost codes.</wsdl:documentation>
      <wsdl:input message="tns:EnumCostCodesSoapIn"/>
      <wsdl:output message="tns:EnumCostCodesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="EnumNetStampsLayouts">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of NetStamps layouts.</wsdl:documentation>
      <wsdl:input message="tns:EnumNetStampsLayoutsSoapIn"/>
      <wsdl:output message="tns:EnumNetStampsLayoutsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="FinishAccountVerification">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Finish account verification for phone.</wsdl:documentation>
      <wsdl:input message="tns:FinishAccountVerificationSoapIn"/>
      <wsdl:output message="tns:FinishAccountVerificationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="FinishPasswordReset">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Finish a password reset, setting the permanent password to a new password.</wsdl:documentation>
      <wsdl:input message="tns:FinishPasswordResetSoapIn"/>
      <wsdl:output message="tns:FinishPasswordResetSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get account information, including postage balance.</wsdl:documentation>
      <wsdl:input message="tns:GetAccountInfoSoapIn"/>
      <wsdl:output message="tns:GetAccountInfoSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistory">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of transactions.</wsdl:documentation>
      <wsdl:input message="tns:GetBalanceHistorySoapIn"/>
      <wsdl:output message="tns:GetBalanceHistorySoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistoryByToken">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of transactions.</wsdl:documentation>
      <wsdl:input message="tns:GetBalanceHistoryByTokenSoapIn"/>
      <wsdl:output message="tns:GetBalanceHistoryByTokenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetBranding">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get Branding.</wsdl:documentation>
      <wsdl:input message="tns:GetBrandingSoapIn"/>
      <wsdl:output message="tns:GetBrandingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetCarrierPickupList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get the existing carrier pickup requests.</wsdl:documentation>
      <wsdl:input message="tns:GetCarrierPickupListSoapIn"/>
      <wsdl:output message="tns:GetCarrierPickupListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetChangePlanStatus">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get status of plan change.</wsdl:documentation>
      <wsdl:input message="tns:GetChangePlanStatusSoapIn"/>
      <wsdl:output message="tns:GetChangePlanStatusSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetCodewordQuestions">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Retrieve codeword questions for user for starting password reset.</wsdl:documentation>
      <wsdl:input message="tns:GetCodewordQuestionsSoapIn"/>
      <wsdl:output message="tns:GetCodewordQuestionsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetImageList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get the images uploaded by the user.</wsdl:documentation>
      <wsdl:input message="tns:GetImageListSoapIn"/>
      <wsdl:output message="tns:GetImageListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetNetStampsImages">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get NetStamps Images.</wsdl:documentation>
      <wsdl:input message="tns:GetNetStampsImagesSoapIn"/>
      <wsdl:output message="tns:GetNetStampsImagesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetNotificationSettings">
      <wsdl:input message="tns:GetNotificationSettingsSoapIn"/>
      <wsdl:output message="tns:GetNotificationSettingsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of shipments.</wsdl:documentation>
      <wsdl:input message="tns:GetShipmentListSoapIn"/>
      <wsdl:output message="tns:GetShipmentListSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentListByToken">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of shipments.</wsdl:documentation>
      <wsdl:input message="tns:GetShipmentListByTokenSoapIn"/>
      <wsdl:output message="tns:GetShipmentListByTokenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetSupportedCountries">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get list of supported countries.</wsdl:documentation>
      <wsdl:input message="tns:GetSupportedCountriesSoapIn"/>
      <wsdl:output message="tns:GetSupportedCountriesSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="GetURL">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get URL for a Stamps.com web page.</wsdl:documentation>
      <wsdl:input message="tns:GetURLSoapIn"/>
      <wsdl:output message="tns:GetURLSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ListPaymentMethods">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Add a payment method for the user.</wsdl:documentation>
      <wsdl:input message="tns:ListPaymentMethodsSoapIn"/>
      <wsdl:output message="tns:ListPaymentMethodsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ModifyBranding">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Modify Branding.</wsdl:documentation>
      <wsdl:input message="tns:ModifyBrandingSoapIn"/>
      <wsdl:output message="tns:ModifyBrandingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ModifyCarrierPickup">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Modify existing carrier pickup request.</wsdl:documentation>
      <wsdl:input message="tns:ModifyCarrierPickupSoapIn"/>
      <wsdl:output message="tns:ModifyCarrierPickupSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ModifyNotificationSetting">
      <wsdl:input message="tns:ModifyNotificationSettingSoapIn"/>
      <wsdl:output message="tns:ModifyNotificationSettingSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PlaceOrder">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Place Store Orders.</wsdl:documentation>
      <wsdl:input message="tns:PlaceOrderSoapIn"/>
      <wsdl:output message="tns:PlaceOrderSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PriceOrder">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Price Store Orders.</wsdl:documentation>
      <wsdl:input message="tns:PriceOrderSoapIn"/>
      <wsdl:output message="tns:PriceOrderSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PurchasePostage">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Purchase additional postage.</wsdl:documentation>
      <wsdl:input message="tns:PurchasePostageSoapIn"/>
      <wsdl:output message="tns:PurchasePostageSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="RecoverUsername">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Recover Username.</wsdl:documentation>
      <wsdl:input message="tns:RecoverUsernameSoapIn"/>
      <wsdl:output message="tns:RecoverUsernameSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="RegisterShippingProviderAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Create an ShippingProviderAccount</wsdl:documentation>
      <wsdl:input message="tns:RegisterShippingProviderAccountSoapIn"/>
      <wsdl:output message="tns:RegisterShippingProviderAccountSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ReprintIndicium">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Begin Reprint Indicium</wsdl:documentation>
      <wsdl:input message="tns:ReprintIndiciumSoapIn"/>
      <wsdl:output message="tns:ReprintIndiciumSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ResubmitPurchase">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Resubmit Purchase.</wsdl:documentation>
      <wsdl:input message="tns:ResubmitPurchaseSoapIn"/>
      <wsdl:output message="tns:ResubmitPurchaseSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ScheduleCarrierPickup">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Schedule carrier pickup.</wsdl:documentation>
      <wsdl:input message="tns:ScheduleCarrierPickupSoapIn"/>
      <wsdl:output message="tns:ScheduleCarrierPickupSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SetAutoBuy">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Set auto-buy settings</wsdl:documentation>
      <wsdl:input message="tns:SetAutoBuySoapIn"/>
      <wsdl:output message="tns:SetAutoBuySoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SetCodeWords">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Set CodeWord information</wsdl:documentation>
      <wsdl:input message="tns:SetCodeWordsSoapIn"/>
      <wsdl:output message="tns:SetCodeWordsSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="StartAccountVerification">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Start account verification for phone.</wsdl:documentation>
      <wsdl:input message="tns:StartAccountVerificationSoapIn"/>
      <wsdl:output message="tns:StartAccountVerificationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="StartPasswordReset">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Start a password reset by sending a temporary password to the e-mail address on file.</wsdl:documentation>
      <wsdl:input message="tns:StartPasswordResetSoapIn"/>
      <wsdl:output message="tns:StartPasswordResetSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="TrackShipment">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Get tracking events for shipment.</wsdl:documentation>
      <wsdl:input message="tns:TrackShipmentSoapIn"/>
      <wsdl:output message="tns:TrackShipmentSoapOut"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SwsimV135Soap" type="tns:SwsimV135Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetRates">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetRates" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateIndicium">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateIndicium" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateEnvelopeIndicium">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateEnvelopeIndicium" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateMailingLabelIndicia">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateMailingLabelIndicia" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateNetStampsIndicia">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateNetStampsIndicia" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegisterAccount">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RegisterAccount" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddCarrier">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddCarrier" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddImage">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddImage" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUserPaymentMethod">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddUserPaymentMethod" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateBridgeAuthenticator">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateBridgeAuthenticator" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateUser">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateUser" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateWithTransferAuthenticator">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateWithTransferAuthenticator" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelAccount">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelAccount" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelCarrierPickup">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelCarrierPickup" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelIndicium">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelIndicium" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeDefaultPaymentMethod">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangeDefaultPaymentMethod" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePassword">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangePassword" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePlan">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangePlan" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckCarrierPickupAvailability">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CheckCarrierPickupAvailability" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CleanseAddress">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CleanseAddress" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBranding">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateBranding" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateManifest">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateManifest" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateNotificationSetting">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateNotificationSetting" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentNotification">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateShipmentNotification" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteBranding">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteBranding" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCarrier">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteCarrier" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteImage">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteImage" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNotificationSetting">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteNotificationSetting" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserPaymentMethod">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteUserPaymentMethod" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumCodeWordTypes">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumCodeWordTypes" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumCostCodes">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumCostCodes" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumNetStampsLayouts">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumNetStampsLayouts" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FinishAccountVerification">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/FinishAccountVerification" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FinishPasswordReset">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/FinishPasswordReset" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetAccountInfo" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistory">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBalanceHistory" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistoryByToken">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBalanceHistoryByToken" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBranding">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBranding" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCarrierPickupList">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetCarrierPickupList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetChangePlanStatus">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetChangePlanStatus" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCodewordQuestions">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetCodewordQuestions" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetImageList">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetImageList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNetStampsImages">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetNetStampsImages" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotificationSettings">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetNotificationSettings" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentList">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetShipmentList" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentListByToken">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetShipmentListByToken" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupportedCountries">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetSupportedCountries" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetURL">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetURL" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListPaymentMethods">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ListPaymentMethods" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyBranding">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyBranding" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyCarrierPickup">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyCarrierPickup" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyNotificationSetting">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyNotificationSetting" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PlaceOrder">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PlaceOrder" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PriceOrder">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PriceOrder" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PurchasePostage">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PurchasePostage" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RecoverUsername">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RecoverUsername" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegisterShippingProviderAccount">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RegisterShippingProviderAccount" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReprintIndicium">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ReprintIndicium" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResubmitPurchase">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ResubmitPurchase" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ScheduleCarrierPickup">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ScheduleCarrierPickup" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetAutoBuy">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/SetAutoBuy" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCodeWords">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/SetCodeWords" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartAccountVerification">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/StartAccountVerification" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartPasswordReset">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/StartPasswordReset" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TrackShipment">
      <soap:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/TrackShipment" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="SwsimV135Soap12" type="tns:SwsimV135Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GetRates">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetRates" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateIndicium">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateIndicium" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateEnvelopeIndicium">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateEnvelopeIndicium" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateMailingLabelIndicia">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateMailingLabelIndicia" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateNetStampsIndicia">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateNetStampsIndicia" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegisterAccount">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RegisterAccount" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddCarrier">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddCarrier" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddImage">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddImage" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUserPaymentMethod">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AddUserPaymentMethod" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateBridgeAuthenticator">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateBridgeAuthenticator" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateUser">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateUser" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AuthenticateWithTransferAuthenticator">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/AuthenticateWithTransferAuthenticator" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelAccount">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelAccount" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelCarrierPickup">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelCarrierPickup" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelIndicium">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CancelIndicium" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangeDefaultPaymentMethod">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangeDefaultPaymentMethod" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePassword">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangePassword" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePlan">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ChangePlan" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckCarrierPickupAvailability">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CheckCarrierPickupAvailability" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CleanseAddress">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CleanseAddress" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBranding">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateBranding" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateManifest">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateManifest" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateNotificationSetting">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateNotificationSetting" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentNotification">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/CreateShipmentNotification" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteBranding">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteBranding" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCarrier">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteCarrier" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteImage">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteImage" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNotificationSetting">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteNotificationSetting" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteUserPaymentMethod">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/DeleteUserPaymentMethod" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumCodeWordTypes">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumCodeWordTypes" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumCostCodes">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumCostCodes" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnumNetStampsLayouts">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/EnumNetStampsLayouts" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FinishAccountVerification">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/FinishAccountVerification" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FinishPasswordReset">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/FinishPasswordReset" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountInfo">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetAccountInfo" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistory">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBalanceHistory" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBalanceHistoryByToken">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBalanceHistoryByToken" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBranding">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetBranding" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCarrierPickupList">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetCarrierPickupList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetChangePlanStatus">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetChangePlanStatus" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCodewordQuestions">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetCodewordQuestions" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetImageList">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetImageList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNetStampsImages">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetNetStampsImages" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotificationSettings">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetNotificationSettings" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentList">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetShipmentList" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetShipmentListByToken">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetShipmentListByToken" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSupportedCountries">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetSupportedCountries" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetURL">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/GetURL" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListPaymentMethods">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ListPaymentMethods" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyBranding">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyBranding" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyCarrierPickup">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyCarrierPickup" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ModifyNotificationSetting">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ModifyNotificationSetting" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PlaceOrder">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PlaceOrder" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PriceOrder">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PriceOrder" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PurchasePostage">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/PurchasePostage" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RecoverUsername">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RecoverUsername" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RegisterShippingProviderAccount">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/RegisterShippingProviderAccount" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReprintIndicium">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ReprintIndicium" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResubmitPurchase">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ResubmitPurchase" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ScheduleCarrierPickup">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/ScheduleCarrierPickup" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetAutoBuy">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/SetAutoBuy" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetCodeWords">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/SetCodeWords" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartAccountVerification">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/StartAccountVerification" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartPasswordReset">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/StartPasswordReset" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TrackShipment">
      <soap12:operation soapAction="http://stamps.com/xml/namespace/2023/07/swsim/SwsimV135/TrackShipment" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SwsimV135">
    <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Stamps.com Web Services for Individual Meters (SWS/IM) Version 135</wsdl:documentation>
    <wsdl:port name="SwsimV135Soap" binding="tns:SwsimV135Soap">
      <soap:address location="https://swsim.stamps.com/swsim/swsimv135.asmx"/>
    </wsdl:port>
    <wsdl:port name="SwsimV135Soap12" binding="tns:SwsimV135Soap12">
      <soap12:address location="https://swsim.stamps.com/swsim/swsimv135.asmx"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
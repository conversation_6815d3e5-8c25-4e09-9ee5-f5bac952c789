<?php
/**
 * Stamps API wrapper class.
 *
 * @package woocommerce-shipping-stamps
 */

/**
 * WC_Stamps_Integration class.
 */
class WC_Stamps_Integration {

	/**
	 * Constructor.
	 */
	public function __construct() {
		add_action( 'before_woocommerce_init', array( $this, 'declare_hpos_compatibility' ) );
		add_filter( 'woocommerce_translations_updates_for_woocommerce-shipping-stamps', '__return_true' );
		add_action( 'after_setup_theme', array( $this, 'load_plugin_textdomain' ) );
		add_action( 'admin_init', array( $this, 'activation_check' ) );
		add_action( 'init', array( $this, 'plugin_init' ) );
		add_filter( 'plugin_action_links_' . plugin_basename( WC_STAMPS_INTEGRATION_FILE ), array( $this, 'plugin_action_links' ) );
	}

	/**
	 * Declare High-Performance Order Storage (HPOS) compatibility
	 *
	 * @see https://github.com/woocommerce/woocommerce/wiki/High-Performance-Order-Storage-Upgrade-Recipe-Book#declaring-extension-incompatibility
	 *
	 * @return void
	 */
	public function declare_hpos_compatibility() {
		if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
			\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', 'woocommerce-shipping-stamps/woocommerce-shipping-stamps.php' );
		}
	}

	/**
	 * Check SOAP support on activation
	 */
	public function activation_check() {
		if ( ! class_exists( 'SoapClient' ) ) {
			deactivate_plugins( plugin_basename( WC_STAMPS_INTEGRATION_FILE ) );
			wp_die( 'Sorry, but you cannot run this plugin, it requires the <a href="http://php.net/manual/en/class.soapclient.php">SOAP</a> support on your server to function.' );
		}
	}

	/**
	 * Plugin action links.
	 *
	 * @since 1.3.3
	 * @version 1.3.3
	 *
	 * @param array $links Plugin action links.
	 *
	 * @return array Plugin action links.
	 */
	public function plugin_action_links( $links ) {
		$plugin_links = array(
			'<a href="' . admin_url( 'admin.php?page=wc-settings&tab=stamps' ) . '">' . __( 'Settings', 'woocommerce-shipping-stamps' ) . '</a>',
			'<a href="http://docs.woocommerce.com/">' . __( 'Support', 'woocommerce-shipping-stamps' ) . '</a>',
			'<a href="https://docs.woocommerce.com/document/woocommerce-shipping-stamps/">' . __( 'Docs', 'woocommerce-shipping-stamps' ) . '</a>',
		);
		return array_merge( $plugin_links, $links );
	}

	/**
	 * Load Localisation files.
	 *
	 * @return void
	 */
	public function load_plugin_textdomain() {
		load_plugin_textdomain( 'woocommerce-shipping-stamps', false, plugin_basename( WC_STAMPS_INTEGRATION_FILE ) . '/languages' );
	}

	/**
	 * Initialize plugin.
	 *
	 * @return void
	 */
	public function plugin_init() {
		require_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-privacy.php';
		include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-settings.php';

		$test_mode = defined( 'WC_STAMPS_TEST_MODE' ) && WC_STAMPS_TEST_MODE;
		if ( $test_mode ) {
			define( 'WC_STAMPS_INTEGRATION_WSDL_FILE', 'test-swsimv135.wsdl' );
			define( 'WC_STAMPS_INTEGRATION_AUTH_ENDPOINT', 'https://api.woocommerce.com/integrations/auth/stampssandbox' );
		} else {
			define( 'WC_STAMPS_INTEGRATION_WSDL_FILE', 'swsimv135.wsdl' );
			define( 'WC_STAMPS_INTEGRATION_AUTH_ENDPOINT', 'https://api.woocommerce.com/integrations/auth/stamps' );
		}

		// Stamps.com rate addons version.
		define( 'WC_STAMPS_RATE_ADDONS_VERSION', '20' );

		include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-api.php';
		include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-balance.php';

		if ( is_admin() && current_user_can( 'manage_woocommerce' ) ) { // phpcs:ignore WordPress.WP.Capabilities -- WooCommerce capability
			include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-order.php';
			include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-post-types.php';
			include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-labels.php';
			include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-label.php';
			include_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-settings.php';
		}
	}
}

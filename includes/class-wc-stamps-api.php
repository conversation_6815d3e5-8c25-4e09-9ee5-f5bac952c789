<?php
/**
 * Stamps API wrapper class.
 *
 * @package woocommerce-shipping-stamps
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

require_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-utils.php';

use WooCommerce\Stamps\Utils;

/**
 * Stamps API wrapper.
 *
 * Used to interact with the Stamps API
 */
class WC_Stamps_API {

	/**
	 * Instance of SoapClient.
	 *
	 * @var SoapClient
	 */
	private static $client = false;

	/**
	 * Authenticator.
	 *
	 * @var string|WP_Error.
	 */
	private static $authenticator = false;

	/**
	 * Instance of WC_Logger.
	 *
	 * @var WC_Logger
	 */
	private static $logger = false;

	/**
	 * Whether logging is enabled or not ('yes' or 'no').
	 *
	 * @var string
	 */
	private static $logging_enabled = null;

	/**
	 * Get Stamps.com API rate addons property.
	 *
	 * @return string Addon property name.
	 */
	public static function get_addon_property() {
		return 'AddOnV' . WC_STAMPS_RATE_ADDONS_VERSION;
	}

	/**
	 * Get Stamps.com API rate addons property.
	 *
	 * @return string Addon property name.
	 */
	public static function get_addon_type_property() {
		return 'AddOnTypeV' . WC_STAMPS_RATE_ADDONS_VERSION;
	}

	/**
	 * Get surcharges info.
	 *
	 * @return array
	 */
	public static function get_surcharges() {
		return array(
			'SUR-A-ER'     => __( 'Extended (or Extended Residential) Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-EV'     => __( 'Declared Value Fee (sometimes labeled "Excessive Value")', 'woocommerce-shipping-stamps' ),
			'SUR-A-FUEL'   => __( 'Fuel Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-OS'     => __( 'Oversize Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-OW'     => __( 'Overweight Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-RAD'    => __( 'Residential or Remote Area Delivery Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-RD'     => __( 'Residential Delivery Surcharge', 'woocommerce-shipping-stamps' ),
			'SUR-A-AHS'    => __( 'Additional Handling Surcharge (e.g., for special packaging or non-machinable parcels)', 'woocommerce-shipping-stamps' ),
			'SUR-A-NCS'    => __( 'Non-Conveyable or Non-Compliance Surcharge (depends on the carrier\'s terminology)', 'woocommerce-shipping-stamps' ),
			'SUR-A-DAS'    => __( 'Delivery Area Surcharge (applies to certain ZIP codes)', 'woocommerce-shipping-stamps' ),
			'SUR-A-DAE'    => __( 'Delivery Area Extended (similar to DAS but for more remote locations)', 'woocommerce-shipping-stamps' ),
			'SUR-A-RAE'    => __( 'Residential Area Extended (some carriers combine residential + remote area)', 'woocommerce-shipping-stamps' ),
			'SUR-A-NM'     => __( 'Non-Machinable Surcharge (for parcels outside standard machine processing)', 'woocommerce-shipping-stamps' ),
			'SUR-A-NSDIM1' => __( 'Non-Standard Dimension 1 (package exceeds certain length/size thresholds)', 'woocommerce-shipping-stamps' ),
			'SUR-A-NSDIM2' => __( 'Non-Standard Dimension 2 (package exceeds a higher dimension threshold, typically larger than NSDIM1)', 'woocommerce-shipping-stamps' ),
			'SUR-A-NSVOL'  => __( 'Non-Standard Volume (package dimensions/volume exceed normal processing limits)', 'woocommerce-shipping-stamps' ),
		);
	}

	/**
	 * Return an array of surcharge name based on type.
	 *
	 * @param string $surcharge_code Surcharge code.
	 *
	 * @return string.
	 */
	public static function get_surcharge_name( string $surcharge_code ): string {
		$surcharges = self::get_surcharges();

		return ! empty( $surcharges[ $surcharge_code ] ) ? $surcharges[ $surcharge_code ] : $surcharge_code;
	}

	/**
	 * Get SOAP client for Stamps service.
	 *
	 * @return SoapClient
	 * @throws \SoapFault In case the workaround failed too.
	 */
	public static function get_client() {
		if ( ! self::$client ) {
			try {
				self::$client = new SoapClient( plugin_dir_path( __DIR__ ) . 'includes/wsdl/' . WC_STAMPS_INTEGRATION_WSDL_FILE, array( 'trace' => 1 ) );
			} catch ( SoapFault $e ) {
				self::log_soap_fault( $e, 'SoapFault during client construction' );

				// Work around in case the first attempt over ssl fails.
				self::$client = new SoapClient(
					plugin_dir_path( __DIR__ ) . 'includes/wsdl/' . WC_STAMPS_INTEGRATION_WSDL_FILE,
					array(
						'trace'          => 1,
						'stream_context' => stream_context_create(
							array(
								'ssl' => array(
									'verify_peer'       => false,
									'verify_peer_name'  => true,
									'allow_self_signed' => false,
								),
							)
						),
					)
				);
			}
		}

		return self::$client;
	}

	/**
	 * Log a message.
	 *
	 * @param string $message Message to log.
	 */
	public static function log( $message ) {
		// Cache it, so we don't call `get_option()` everytime log is called.
		if ( is_null( self::$logging_enabled ) ) {
			self::$logging_enabled = get_option( 'wc_settings_stamps_logging', 'no' );
		}

		if ( 'yes' !== self::$logging_enabled ) {
			return;
		}

		if ( ! self::$logger ) {
			self::$logger = new WC_Logger();
		}

		self::$logger->add( 'stamps', $message );
	}

	/**
	 * Log SoapFault.
	 *
	 * @since 1.3.3
	 * @version 1.3.3
	 *
	 * @param SoapFault $e      Instance of SoapFault.
	 * @param string    $prefix Message prefix.
	 */
	private static function log_soap_fault( SoapFault $e, $prefix ) {
		self::log( $prefix . ' : ' . $e->getMessage() );
		self::log( 'Detailed exception:' );
		self::log( print_r( $e, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r -- This is a log function.
	}

	/**
	 * Make an API request.
	 *
	 * @param string $endpoint Endpoint.
	 * @param array  $request  Request params.
	 * @param bool   $retry    Whether to retry when failed.
	 *
	 * @return object|WP_Error Response on success, WP_Error if failed.
	 */
	public static function do_request( $endpoint, $request = array(), $retry = false ) {

		@ini_set( 'soap.wsdl_cache_enabled', 0 ); // phpcs:ignore --- It needs to use ini_use

		try {

			if ( empty( $request['Authenticator'] ) ) {
				$authenticator = self::get_authenticator();
				if ( is_wp_error( $authenticator ) ) {
					return $authenticator;
				}
				$request['Authenticator'] = $authenticator;
			}

			$mask = array(
				'Address'       => array(
					'FullName' => '*** ***',
					'Address1' => '***',
					'Address2' => '***',
					'City'     => '***',
					'State'    => '***',
					'ZIPCode'  => '***',
				),
				'Authenticator' => '***',
			);

			$obfuscated_request = array_merge( $request, array_intersect_key( $mask, $request ) );
			self::log( "Endpoint $endpoint Request: " . wc_print_r( $obfuscated_request, true ) );

			$client   = self::get_client();
			$response = $client->$endpoint( $request );

			$mask = array(
				'Address'       => array(
					'FullName'     => '*** ***',
					'Address1'     => '***',
					'Address2'     => '***',
					'City'         => '***',
					'State'        => '***',
					'ZIPCode'      => '***',
					'ZIPCodeAddOn' => '***',
					'CleanseHash'  => '***',
					'OverrideHash' => '***',
				),
				'Authenticator' => '***',
			);

			$obfuscated_response = array_merge( (array) $response, array_intersect_key( $mask, (array) $response ) );
			self::log( "Endpoint $endpoint Response: " . print_r( $obfuscated_response, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r -- This is a log function.

			self::update_authenticator( $response );
			self::update_balance( $response );
			return $response;
		} catch ( SoapFault $e ) {
			self::log_soap_fault( $e, 'SoapFault during call to endpoint ' . $endpoint );

			// Try again if authenticator is bad.
			if ( ! $retry && isset( $e->detail->sdcerror ) && ( strstr( $e->detail->sdcerror, '002b0201' ) || strstr( $e->detail->sdcerror, '002b0202' ) || strstr( $e->detail->sdcerror, '002b0203' ) || strstr( $e->detail->sdcerror, '002b0204' ) ) ) {
				self::$authenticator      = false;
				$request['Authenticator'] = false;
				delete_transient( 'stamps_authenticator' );
				return self::do_request( $endpoint, $request, true );
			}

			return new WP_Error( $e->faultcode, $e->faultstring );
		}
	}

	/**
	 * Authenticate a user.
	 *
	 * @return string|WP_Error
	 */
	public static function authenticate() {
		$response = wp_remote_post(
			WC_STAMPS_INTEGRATION_AUTH_ENDPOINT,
			array(
				'method'      => 'POST',
				'timeout'     => 10,
				'httpversion' => '1.1',
				'user-agent'  => 'WooCommerce/' . WC_VERSION . '; ' . get_bloginfo( 'url' ),
				'body'        => array(
					'username' => get_option( 'wc_settings_stamps_username' ),
					'password' => get_option( 'wc_settings_stamps_password' ),
				),
			)
		);
		if ( ! is_wp_error( $response ) && ! empty( $response['body'] ) && ! strstr( $response['body'], 'error' ) ) {
			self::$authenticator = trim( $response['body'], '"' );
			set_transient( 'stamps_authenticator', self::$authenticator );
			return self::$authenticator;
		} elseif ( is_wp_error( $response ) ) {
			self::log( 'Error getting stamps_authenticator: ' . $response->get_error_message() );
			return new WP_Error( 'authentication_failed', $response->get_error_message() );
		} else {
			self::log( 'Error getting stamps_authenticator: ' . print_r( $response, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r -- This is a log function.
			$decoded_response = json_decode( $response['body'], true );
			if ( JSON_ERROR_NONE === json_last_error() && isset( $decoded_response['error'] ) ) {
				return new WP_Error( 'authentication_failed', $decoded_response['error'] );
			}
		}
		return new WP_Error( 'authentication_failed', 'Unable to authenticate with Stamps.com' );
	}

	/**
	 * Get authenticator for requests.
	 *
	 * @return string|WP_Error
	 */
	public static function get_authenticator() {
		if ( self::$authenticator ) {
			return self::$authenticator;
		}

		$authenticator = get_transient( 'stamps_authenticator' );
		if ( $authenticator ) {
			return $authenticator;
		}

		$authenticator = self::authenticate();
		if ( is_wp_error( $authenticator ) || ! empty( $authenticator ) ) {
			return $authenticator;
		}

		return new WP_Error( 'authentication_failed', 'Authentication Failed' );
	}

	/**
	 * Update authenticator after a request.
	 *
	 * @param object $response Response from SOAP request.
	 */
	public static function update_authenticator( $response ) {
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
		if ( isset( $response->Authenticator ) ) {
			self::$authenticator = $response->Authenticator;
			set_transient( 'stamps_authenticator', self::$authenticator );
		}
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
	}

	/**
	 * Update stamps balance.
	 *
	 * Called after every successful request (by self::do_request) to the stamps.com
	 * API.
	 *
	 * Note that this also kicks off the top-up code - that will result in
	 * postage being purchased for accounts that fall below the merchant's
	 * minimum (if any).
	 *
	 * @param object $response Response from SOAP request.
	 */
	public static function update_balance( $response ) {
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
		if ( isset( $response->PostageBalance ) ) {
			set_transient( 'wc_stamps_balance', $response->PostageBalance->AvailablePostage, DAY_IN_SECONDS );
			set_transient( 'wc_stamps_control_total', $response->PostageBalance->ControlTotal, DAY_IN_SECONDS );

			WC_Stamps_Balance::check_balance( $response->PostageBalance->AvailablePostage );
		}
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
	}

	/**
	 * Purchase postage on behalf of the user.
	 *
	 * @param int   $amount        Amount to top up. Must be an integer.
	 * @param float $control_total This is the amount of postage that the user
	 *                             has CONSUMED over the LIFETIME of the account.
	 *
	 * @return object|WP_Error Response from SOAP request, WP_Error if failed.
	 */
	public static function purchase_postage( $amount, $control_total ) {
		$request = array(
			'PurchaseAmount' => absint( $amount ),
			'ControlTotal'   => number_format( $control_total, 4, '.', '' ),
		);
		return self::do_request( 'PurchasePostage', $request );
	}

	/**
	 * Check purchase status.
	 *
	 * @param  string $transaction_id Transaction ID.
	 * @return object|WP_Error
	 */
	public static function get_purchase_status( $transaction_id ) {
		$request = array(
			'TransactionID' => $transaction_id,
		);
		return self::do_request( 'GetPurchaseStatus', $request );
	}

	/**
	 * Check From & To addresses.
	 *
	 * @param array $request Request to check.
	 * @return true|WP_Error
	 */
	public static function check_address( $request ) {
		if ( empty( $request['From']['Address1'] ) ) {
			return new WP_Error( 'stamps', 'Shipping return address 1 is empty! Please set the address from WooCommerce >> Settings >> Stamps.com settings.' );
		}

		if ( empty( $request['From']['City'] ) ) {
			return new WP_Error( 'stamps', 'Shipping return city is empty! Please set the address from WooCommerce >> Settings >> Stamps.com settings.' );
		}

		if ( empty( $request['From']['State'] ) ) {
			return new WP_Error( 'stamps', 'Shipping return state is empty! Please set the address from WooCommerce >> Settings >> Stamps.com settings.' );
		}

		if ( empty( $request['From']['ZIPCode'] ) ) {
			return new WP_Error( 'stamps', 'Shipping return ZIP code is empty! Please set the address from WooCommerce >> Settings >> Stamps.com settings.' );
		}

		if ( empty( $request['To']['Address1'] ) ) {
			return new WP_Error( 'stamps', 'Shipping address 1 is empty!' );
		}

		if ( empty( $request['To']['City'] ) ) {
			return new WP_Error( 'stamps', 'Shipping city is empty!' );
		}

		if ( empty( $request['To']['Country'] ) ) {
			return new WP_Error( 'stamps', 'Shipping country is empty!' );
		}

		return true;
	}

	/**
	 * Get account info.
	 *
	 * @return string|WP_Error
	 */
	public static function get_account_info() {
		return self::do_request( 'getAccountInfo' );
	}

	/**
	 * Checks to see if country is a US territory.
	 * This is needed because WC treats US territories
	 * such as Puerto Rico as a country rather than a
	 * US state.
	 *
	 * @since 1.3.15
	 * @param string $country The shipping country.
	 * @return bool
	 */
	public static function is_us_territory( $country ) {
		// List of US territories.
		$list = array(
			'PR',
			'VI',
		);

		return in_array( $country, $list, true );
	}

	/**
	 * Verify an address.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return array|WP_Error
	 */
	public static function verify_address( $order ) {
		$request = array(
			'Address' => self::get_shipping_address( $order ),
		);

		$result = self::do_request( 'CleanseAddress', $request );

		if ( is_wp_error( $result ) ) {
			return $result;
		}
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
		$address = $result->Address ?? null;

		if (
			! isset(
				$address->CleanseHash,
				$address->OverrideHash,
				$address->FullName,
				$address->Address1,
				$address->City,
			)
			|| ( ! isset( $address->Province ) && ! isset( $address->State ) )

		) {
			return new WP_Error( 'wc_stamps_verify_address_error', 'Returned address from Stamps.com is not complete' );
		}

		if ( $result->AddressMatch ) {
			// If we get a ZIP and a ZIP+4 returned by Stamps.com (which we usually will), pack
			// them into the postcode. We need both parts to use this cleansed address when
			// buying a label (otherwise the Cleanse Hash will not match).
			$zip_code       = isset( $address->ZIPCode ) ? $address->ZIPCode : '';
			$zip_code_addon = isset( $address->ZIPCodeAddOn ) ? $address->ZIPCodeAddOn : '';
			if ( ! empty( $zip_code_addon ) ) {
				$zip_code .= '-' . $zip_code_addon;
			}

			// Return address in our own format.
			return array(
				'matched'      => true,
				'matched_zip'  => true,
				'hash'         => $address->CleanseHash,
				'overide_hash' => $address->OverrideHash,
				'address'      => array(
					'full_name' => $address->FullName,
					'company'   => isset( $address->Company ) ? $address->Company : '',
					'address_1' => $address->Address1,
					'address_2' => isset( $address->Address2 ) ? $address->Address2 : '',
					'city'      => $address->City,
					'state'     => isset( $address->Province ) ? $address->Province : $address->State,
					'postcode'  => isset( $address->PostalCode ) ? $address->PostalCode : $zip_code,
					'country'   => isset( $address->Country ) ? $address->Country : '',
				),
			);
		}

		if ( 'US' === $order->get_shipping_country() ) {
			// User can proceed anyway.
			if ( $result->CityStateZipOK ) {
				return array(
					'matched'      => false,
					'matched_zip'  => true,
					'overide_hash' => $address->OverrideHash,
				);
			}
		}
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase

		return array(
			'matched'     => false,
			'matched_zip' => false,
		);
	}

	/**
	 * Get rates for a package.
	 *
	 * @param  WC_Order $order Order object.
	 * @param  array    $args  Request args.
	 *
	 * @return array|bool|string|\WP_Error
	 */
	public static function get_rates( $order, $args ) {
		$request = array(
			'Rate' => array(
				'From'          => self::get_store_address(),
				'To'            => self::get_shipping_address( $order ),
				'WeightLb'      => floor( $args['weight'] ),
				'WeightOz'      => number_format( ( $args['weight'] - floor( $args['weight'] ) ) * 16, 2 ),
				'ShipDate'      => $args['date'],
				'InsuredValue'  => $args['value'],
				'CODValue'      => $args['value'],
				'DeclaredValue' => $args['value'],
				'Length'        => $args['length'],
				'Width'         => $args['width'],
				'Height'        => $args['height'],
				'PackageType'   => $args['type'],
				'PrintLayout'   => 'Normal4X6',
				'ContentType'   => $args['content_type'],
			),
		);

		$check_request = self::check_address( $request['Rate'] );
		if ( is_wp_error( $check_request ) ) {
			return $check_request;
		}

		$postcode = $order->get_shipping_postcode();

		if ( ! empty( $postcode ) ) {
			$request['Rate']['ToZIPCode'] = $postcode;
		}

		$result = self::do_request( 'GetRates', $request );

		if ( is_wp_error( $result ) ) {
			self::log( 'Error getting rates for request: ' . print_r( $request, true ) . '. Response: ' . print_r( $result, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r -- This is a log function.
			return $result;
		}

		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
		// It is possible $results->Rates is empty or an empty stdClass Object, so let's test for both
		// A safe way to do so is to cast to array and then test for an empty array.
		if ( empty( $result->Rates->Rate ) ) {
			return new WP_Error( 'no_rates', __( 'No rates were returned for the selected package type, weight and dimensions. Please select a different package type and try again.', 'woocommerce-shipping-stamps' ) );
		}

		if ( ! is_array( $result->Rates->Rate ) ) {
			$api_rates = array( $result->Rates->Rate );
		} else {
			$api_rates = $result->Rates->Rate;
		}

		$rates = array();
		foreach ( $api_rates as $rate ) {
			if ( ! isset(
				$rate->Amount,
				$rate->ServiceType,
				$rate->PackageType,
				$rate->ServiceDescription
			) ) {
				continue;
			}

			$surcharges       = self::get_rate_surcharges( $rate );
			$total_surcharges = 0;

			foreach ( $surcharges as $surcharge ) {
				$total_surcharges += floatval( $surcharge['cost'] );
			}

			$total_cost = $total_surcharges + floatval( $rate->Amount );
			$rates[]    = (object) array(
				'cost'             => $total_cost,
				'surcharges'       => $surcharges,
				'total_surcharges' => $total_surcharges,
				'service'          => $rate->ServiceType,
				'package'          => $rate->PackageType,
				'name'             => $rate->ServiceDescription,
				'dim_weighting'    => isset( $rate->DimWeighting ) ? $rate->DimWeighting : 0,
				'rate_object'      => $rate,
			);
		}
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase

		return $rates;
	}

	/**
	 * Get total surcharges.
	 *
	 * @param object $rate Stamps API rate.
	 */
	public static function get_rate_surcharges( $rate ) {
		$rate_surcharges = array();

		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
		if ( empty( $rate->Surcharges ) || ( ! is_array( $rate->Surcharges ) && ! is_object( $rate->Surcharges ) ) ) {
			return $rate_surcharges;
		}

		foreach ( $rate->Surcharges as $surcharge ) {
			if ( empty( $surcharge->Amount ) ) {
				continue;
			}

			$surcharge_type = ! empty( $surcharge->SurchargeType ) ? $surcharge->SurchargeType : esc_html__( 'Other surcharges', 'woocommerce-shipping-stamps' );

			$rate_surcharges[] = array(
				'type' => $surcharge_type,
				'name' => self::get_surcharge_name( $surcharge_type ),
				'cost' => $surcharge->Amount,
			);
		}

		return $rate_surcharges;
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
	}

	/**
	 * Get (purchase) label for a rate.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $args Request args.
	 *
	 * @return array|bool|int|string|\WC_Stamps_Label|\WP_Error
	 * @version 1.3.2
	 */
	public static function purchase_label( $order, $args ) {
		$order_id = $order->get_id();
		$rate     = $args['rate'];
		$customs  = $args['customs'];
		$tx_id    = uniqid( 'wc_' . $order_id . '_' );

		$order->update_meta_data( '_last_label_tx_id', $tx_id );
		$order->save();

		$request = array(
			'IntegratorTxID'    => $tx_id,
			'Rate'              => $rate,
			'SampleOnly'        => get_option( 'wc_settings_stamps_sample_only', 'yes' ) === 'yes',
			'ImageType'         => get_option( 'wc_settings_stamps_image_type', 'Pdf' ),
			'PaperSize'         => get_option( 'wc_settings_stamps_paper_size', 'Default' ),
			'nonDeliveryOption' => $args['non_delivery_option'],
		);

		if ( $customs ) {
			$request['Customs'] = $customs;
		}

		$result = self::do_request( 'CreateIndicium', $request );
		if ( is_wp_error( $result ) ) {
			return $result;
		}

		if ( empty( $result->URL ) ) { // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
			return new WP_Error( 'stamps-api', __( 'Cannot create a label for the package with the requested service.', 'woocommerce-shipping-stamps' ) );
		}

		$label_id = WC_Stamps_Labels::create_label( $order, $result );

		if ( is_wp_error( $label_id ) ) {
			return $label_id;
		}

		return new WC_Stamps_Label( $label_id );
	}

	/**
	 * Cancel a label.
	 *
	 * @param WC_Order $order Order object.
	 * @param string   $tx_id Transaction ID.
	 *
	 * @return bool|WP_Error true on success.
	 */
	public static function cancel_label( $order, $tx_id ) {
		$request = array(
			'StampsTxIDs' => $tx_id,
		);
		$result  = self::do_request( 'CancelIndicium', $request );

		if ( is_wp_error( $result ) ) {
			return $result;
		}

		return true;
	}

	/**
	 * Get a URL to an account page.
	 *
	 * @param  string $endpoint Endpoint.
	 * @return string|bool
	 */
	public static function get_url( $endpoint ) {
		$request = array(
			'URLType'            => $endpoint,
			'ApplicationContext' => '',
		);
		$result  = self::do_request( 'GetURL', $request );

		if ( is_wp_error( $result ) ) {
			return false;
		}

		return esc_url_raw( $result->URL ); // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase -- API returned data.
	}

	/**
	 * Get store address.
	 *
	 * @return array.
	 */
	public static function get_store_address() {
		return array(
			'FullName'    => get_option( 'wc_settings_stamps_full_name' ),
			'Company'     => get_option( 'wc_settings_stamps_company' ),
			'Address1'    => get_option( 'wc_settings_stamps_address_1' ),
			'Address2'    => get_option( 'wc_settings_stamps_address_2' ),
			'City'        => get_option( 'wc_settings_stamps_city' ),
			'State'       => get_option( 'wc_settings_stamps_state' ),
			'ZIPCode'     => get_option( 'wc_settings_stamps_zip' ),
			'Country'     => 'US',
			'PhoneNumber' => get_option( 'wc_settings_stamps_phone' ),
		);
	}

	/**
	 * Get order formatted shipping address.
	 *
	 * @param WC_Order $order Order to get its shipping address.
	 *
	 * @return array.
	 */
	public static function get_shipping_address( $order ) {
		$shipping_country = $order->get_shipping_country();
		$is_us_territory  = self::is_us_territory( $shipping_country );

		$address = array(
			'FullName' => $order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name(),
			'Company'  => $order->get_shipping_company(),
			'Address1' => $order->get_shipping_address_1(),
			'Address2' => $order->get_shipping_address_2(),
			'City'     => $order->get_shipping_city(),
			'Country'  => ( $is_us_territory ) ? 'US' : $shipping_country,
		);

		// Figure out which tag to use for the address hash. We want to use
		// 'CleanseHash' if the merchant accepted stamps.com's changes to the To Address or
		// 'OverrideHash' if the merchant selected to "continue without changes" to the To Address
		// See also WC_Stamps_Order::ajax_override_address.
		$cleanse_hash  = $order->get_meta( '_stamps_hash' );
		$override_hash = $order->get_meta( '_stamps_override_hash' );
		if ( $cleanse_hash === $override_hash ) {
			$address['OverrideHash'] = $override_hash;
		} else {
			$address['CleanseHash'] = $cleanse_hash;
		}

		$postcode = $order->get_shipping_postcode();
		$state    = $order->get_shipping_state();
		if ( 'US' === $shipping_country || $is_us_territory ) {
			$postcode_pieces = explode( '-', $postcode );
			$zipcode         = $postcode_pieces[0];
			$zipcode_addon   = isset( $postcode_pieces[1] ) ? $postcode_pieces[1] : '';

			$address += array(
				'State'   => $state,
				'ZIPCode' => substr( $zipcode, 0, 5 ),
			);

			// Add in the ZIP+4 (ZIPCodeAddOn) if present in the address
			// Otherwise the "To Address Cleanse Hash" match will fail.
			if ( $zipcode_addon ) {
				$address['ZIPCodeAddOn'] = substr( $zipcode_addon, 0, 4 );
			}
		} else {
			$address += array(
				'Province'    => $state,
				'PostalCode'  => $postcode,
				'PhoneNumber' => $order->get_billing_phone(),
			);
		}

		return $address;
	}
}

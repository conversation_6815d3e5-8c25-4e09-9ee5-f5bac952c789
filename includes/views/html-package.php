<?php
/**
 * View template for defining packages from AJAX response.
 *
 * @package woocommerce-shipping-stamps
 */

?>

<p><?php esc_html_e( 'Enter the weight and dimensions for the package being shipped. Dimensions are optional, but may be required for more accurate rating.', 'woocommerce-shipping-stamps' ); ?></p>

<table class="form-table">
	<tr>
		<th><label><?php esc_html_e( 'Package type', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td>
			<select name="stamps_package_type">
				<option value=""><?php esc_html_e( 'Any (return all options)', 'woocommerce-shipping-stamps' ); ?></option>
				<?php
				foreach ( $package_types as $package_type => $package_info ) {
					echo '<option value="' . esc_attr( $package_type ) . '">' . esc_html( $package_info['name'] ) . '</option>';
				}
				?>
			</select> <span class="description"></span>
		</td>
	</tr>
	<tr>
		<th><label><?php esc_html_e( 'Content Type', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td>
			<select name="stamps_content_type">
				<?php
				foreach ( $content_types as $content_type => $content_type_name ) {
					echo '<option ' . selected( $content_type_name, $default_content_type ) . ' value="' . esc_attr( $content_type ) . '">' . esc_html( $content_type_name ) . '</option>';
				}
				?>
			</select>
		</td>
	</tr>
	<tr>
		<th><label><?php esc_html_e( 'Non-delivery action', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td>
			<select name="stamps_non_delivery_option">
				<?php
				foreach ( $non_delivery_options as $option_key => $option_name ) {
					echo '<option ' . selected( $option_key, $default_non_delivery_option, false ) . ' value="' . esc_attr( $option_key ) . '">' . esc_html( $option_name ) . '</option>';
				}
				?>
			</select>
			<span class="description"><?php esc_html_e( 'Action to take when delivery fails or address cannot be reached.', 'woocommerce-shipping-stamps' ); ?></span>
		</td>
	</tr>
	<tr>
		<th><label><?php esc_html_e( 'Ship date', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td><input type="text" value="<?php echo esc_attr( $ship_date ); ?>" name="stamps_package_date" class="stamps-date-picker" /></td>
	</tr>
	<tr>
		<th><label><?php echo esc_html__( 'Weight', 'woocommerce-shipping-stamps' ) . ' (' . esc_html( get_option( 'woocommerce_weight_unit' ) ) . ')'; ?></label></th>
		<td><input type="text" value="<?php echo esc_attr( $total_weight ); ?>" name="stamps_package_weight" /></td>
	</tr>
	<tr>
		<th><label><?php esc_html_e( 'Value', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td><input type="text" value="<?php echo esc_attr( $total_cost ); ?>" name="stamps_package_value" /></td>
	</tr>
	<tr>
		<th><label><?php echo esc_html__( 'Length', 'woocommerce-shipping-stamps' ) . ' (' . esc_html( get_option( 'woocommerce_dimension_unit' ) ) . ')'; ?></label></th>
		<td>
			<input type="text" name="stamps_package_length" />
		</td>
	</tr>
	<tr>
		<th><label><?php echo esc_html__( 'Width', 'woocommerce-shipping-stamps' ) . ' (' . esc_html( get_option( 'woocommerce_dimension_unit' ) ) . ')'; ?></label></th>
		<td>
			<input type="text" name="stamps_package_width" />
		</td>
	</tr>
	<tr>
		<th><label><?php echo esc_html__( 'Height', 'woocommerce-shipping-stamps' ) . ' (' . esc_html( get_option( 'woocommerce_dimension_unit' ) ) . ')'; ?></label></th>
		<td>
			<input type="text" name="stamps_package_height" />
		</td>
	</tr>
</table>

<p><button type="submit" class="button button-primary stamps-action" data-stamps_action="get_rates"><?php esc_html_e( 'Get rates', 'woocommerce-shipping-stamps' ); ?></button></p>

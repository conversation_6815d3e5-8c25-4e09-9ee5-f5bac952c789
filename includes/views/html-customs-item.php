<?php
/**
 * View template to display a single custom item.
 *
 * @package woocommerce-shipping-stamps
 */

?>

<div class="wc-stamps-customs-item">
	<table class="form-table">
		<tr>
			<th><label><?php esc_html_e( 'Description', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="text" name="stamps_customs_item_description[]" value="<?php echo esc_attr( $description ); ?>" maxlength="60" /></td>
		</tr>
		<tr>
			<th><label><?php esc_html_e( 'Quantity', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="number" name="stamps_customs_item_quantity[]" step="1" min="1" value="<?php echo esc_attr( $qty ); ?>" /></td>
		</tr>
		<tr>
			<th><label><?php esc_html_e( 'Value ($)', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="number" name="stamps_customs_item_value[]" step="0.01" min="0.01" value="<?php echo esc_attr( $value ); ?>" /></td>
		</tr>
		<tr>
			<th><label><?php esc_html_e( 'Weight (lbs)', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="number" name="stamps_customs_item_weight[]" step="0.01" min="0.01" value="<?php echo esc_attr( $weight ); ?>" /></td>
		</tr>
		<tr>
			<th><label><?php esc_html_e( 'HS Tariff', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="text" name="stamps_customs_item_hs_tariff[]" placeholder="<?php esc_attr_e( 'optional', 'woocommerce-shipping-stamps' ); ?>" />
		</tr>
		<tr>
			<th><label><?php esc_html_e( 'Country (code) of origin', 'woocommerce-shipping-stamps' ); ?></label></th>
			<td><input type="text" name="stamps_customs_item_origin[]" size="2" maxlength="2" placeholder="<?php esc_attr_e( 'optional', 'woocommerce-shipping-stamps' ); ?>"  />
		</tr>
	</table>
	<a href="#" class="wc-stamps-customs-remove-line"><?php esc_attr_e( 'Remove line', 'woocommerce-shipping-stamps' ); ?></a>
</div>

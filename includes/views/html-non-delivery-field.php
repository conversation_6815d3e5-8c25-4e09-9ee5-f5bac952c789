<?php
/**
 * Non-delivery action field partial template.
 * 
 * This partial template renders the non-delivery action select field
 * that can be included in both rates and customs templates.
 *
 * @package woocommerce-shipping-stamps
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

?>

<table class="form-table">
	<tr>
		<th><label><?php esc_html_e( 'Non-delivery action', 'woocommerce-shipping-stamps' ); ?></label></th>
		<td>
			<select name="stamps_non_delivery_option" id="stamps_non_delivery_option">
				<?php
				foreach ( $non_delivery_options as $option_key => $option_name ) {
					echo '<option ' . selected( $option_key, $default_non_delivery_option, false ) . ' value="' . esc_attr( $option_key ) . '">' . esc_html( $option_name ) . '</option>';
				}
				?>
			</select>
			<span class="description"><?php esc_html_e( 'Action to take when delivery fails or address cannot be reached.', 'woocommerce-shipping-stamps' ); ?></span>
			<div id="stamps_return_fee_notice" class="notice notice-warning inline" style="display: none; margin-top: 10px;">
				<p><?php esc_html_e( 'You may incur additional return package fees.', 'woocommerce-shipping-stamps' ); ?></p>
			</div>
		</td>
	</tr>
</table>

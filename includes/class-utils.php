<?php
/**
 * Utils class.
 *
 * @package woocommerce-shipping-stamps
 */
namespace WooCommerce\Stamps;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WC_Stamps_Utils class
 */
class Utils {
	/**
	 * Return array of Stamps.com package content types.
	 *
	 * @since 2.0.1
	 * @return array
	 */
	public static function get_content_types() {
		return array(
			'Commercial Sample'     => esc_html__( 'Commercial Sample', 'woocommerce-shipping-stamps' ),
			'Dangerous Goods'       => esc_html__( 'Dangerous Goods', 'woocommerce-shipping-stamps' ),
			'Document'              => esc_html__( 'Document', 'woocommerce-shipping-stamps' ),
			'Gift'                  => esc_html__( 'Gift', 'woocommerce-shipping-stamps' ),
			'Humanitarian Donation' => esc_html__( 'Humanitarian Donation', 'woocommerce-shipping-stamps' ),
			'Merchandise'           => esc_html__( 'Merchandise', 'woocommerce-shipping-stamps' ),
			'Returned Goods'        => esc_html__( 'Returned Goods', 'woocommerce-shipping-stamps' ),
			'Other'                 => esc_html__( 'Other', 'woocommerce-shipping-stamps' ),
		);
	}

	/**
	 * Return array of Stamps.com non-delivery options.
	 *
	 * @since 2.1.7
	 * @return array
	 */
	public static function get_non_delivery_options(): array {
		return array(
			'Return'  => esc_html__( 'Return to Sender', 'woocommerce-shipping-stamps' ),
			'Abandon' => esc_html__( 'Abandon', 'woocommerce-shipping-stamps' ),
		);
	}

	/**
	 * Get the default content type.
	 *
	 * @return string
	 */
	public static function get_default_non_delivery_option(): string {
		return 'Return';
	}
}

[![CI](https://github.com/woocommerce/woocommerce-shipping-stamps/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-stamps/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-stamps/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-stamps/actions/workflows/cron_qit.yml)

woocommerce-shipping-stamps
====================

Stamps.com API integration for label printing. Requires server SOAP support.

## NPM Scripts

WooCommerce Shipping Stamps utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.

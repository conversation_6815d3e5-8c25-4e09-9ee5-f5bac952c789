<?php
/**
 * Plugin Name: WooCommerce Stamps.com API integration
 * Plugin URI: https://woocommerce.com/products/woocommerce-shipping-stamps/
 * Description: Stamps.com API integration for label printing. Requires server SOAP support.
 * Version: 2.1.6
 * Author: WooCommerce
 * Author URI: https://woocommerce.com/
 * Text Domain: woocommerce-shipping-stamps
 * Domain Path: /languages
 *
 * Woo: 538435:b0e7af51937d3cdbd6779283d482b6e4
 * Requires Plugins: woocommerce
 * Requires PHP: 7.4
 * Requires at least: 6.7
 * Tested up to: 6.8
 * WC requires at least: 9.8
 * WC tested up to: 10.0
 *
 * Copyright: © 2025 WooCommerce
 * License: GNU General Public License v3.0
 * License URI: http://www.gnu.org/licenses/gpl-3.0.html
 *
 * @package woocommerce-shipping-stamps
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WooCommerce fallback notice.
 *
 * @since 1.3.17
 * @return void
 */
function woocommerce_shipping_stamps_missing_wc_notice() {
	/* translators: %s WC download URL link. */
	echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'Stamps requires WooCommerce to be installed and active. You can download %s here.', 'woocommerce-shipping-stamps' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</strong></p></div>';
}

if ( ! class_exists( 'WC_Stamps_Integration' ) ) {
	define( 'WC_STAMPS_INTEGRATION_VERSION', '2.1.6' ); // WRCS: DEFINED_VERSION.

	if ( ! defined( 'WC_STAMPS_INTEGRATION_FILE' ) ) {
		define( 'WC_STAMPS_INTEGRATION_FILE', __FILE__ );
	}

	if ( ! defined( 'WC_STAMPS_INTEGRATION_ABSPATH' ) ) {
		define( 'WC_STAMPS_INTEGRATION_ABSPATH', trailingslashit( __DIR__ ) );
	}

	require_once WC_STAMPS_INTEGRATION_ABSPATH . 'includes/class-wc-stamps-integration.php';
}

add_action( 'plugins_loaded', 'woocommerce_shipping_stamps_init' );

/**
 * Initializes the extension.
 *
 * @return void | WC_Stamps_Integration
 *@since 1.3.17
 */
function woocommerce_shipping_stamps_init() {
	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'woocommerce_shipping_stamps_missing_wc_notice' );
		return;
	}

	return wc_shipping_stamps();
}

/**
 * Return instance of WC_Stamps_Integration.
 *
 * @since 1.3.3
 * @version 1.3.3
 *
 * @return WC_Stamps_Integration.
 */
function wc_shipping_stamps() {
	static $plugin;

	if ( ! isset( $plugin ) ) {
		$plugin = new WC_Stamps_Integration();
	}

	return $plugin;
}

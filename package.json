{"name": "woocommerce-shipping-stamps", "title": "WooCommerce Shipping Stamps", "version": "2.2.0", "homepage": "https://woocommerce.com/products/woocommerce-shipping-stamps/", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-shipping-stamps.git"}, "devDependencies": {"clean-css-cli": "^4.3.0", "sass": "^1.77.5", "node-wp-i18n": "~1.2.3", "uglify-js": "^3.6.0"}, "assets": {"js": {"min": "assets/js/*.min.js", "js": "assets/js/*.js"}, "styles": {"css": "assets/css/*.css", "sass": "assets/css/*.scss", "cssfolder": "assets/css/"}}, "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true, "paths": {"js": "assets/js/*.js", "js_min": "assets/js/*.min.js", "css": "assets/css/*.css", "sass": "assets/sass", "cssfolder": "assets/css"}}, "scripts": {"prebuild": "rm -rf ./vendor", "build": "pnpm run build:dev && npm run archive", "build:dev": "pnpm run uglify && pnpm run makepot && pnpm run sass", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "preuglify": "rm -f $npm_package_config_paths_js_min", "uglify": "for f in $npm_package_config_paths_js; do file=${f%.js}; node_modules/.bin/uglifyjs $f -c -m > $file.min.js; done", "presass": "rm -f $npm_package_config_paths_css", "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed", "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch", "postsass": "for f in $npm_package_config_paths_css; do echo Processing $f; file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}}